using UnityEngine;
using UnityEngine.AI;

/// <summary>
/// Helper script to automatically set up the test scene with proper NavMesh and components
/// Attach this to an empty GameObject in your scene and it will create the basic setup
/// </summary>
public class SceneSetup : MonoBehaviour
{
    [Header("Auto Setup")]
    public bool autoSetupOnStart = true;
    public bool createTerrain = true;
    public bool createNavMesh = true;
    
    [Header("Prefab References")]
    public GameObject playerPrefab;
    public GameObject teammatePrefab;
    public GameObject enemyPrefab;
    
    [Header("Spawn Settings")]
    public Vector3 playerSpawnPosition = Vector3.zero;
    public Vector3 teammateSpawnPosition = new Vector3(2f, 0f, 0f);
    public Vector3[] enemySpawnPositions = new Vector3[]
    {
        new Vector3(10f, 0f, 10f),
        new Vector3(-10f, 0f, 10f),
        new Vector3(10f, 0f, -10f)
    };
    
    void Start()
    {
        if (autoSetupOnStart)
        {
            SetupScene();
        }
    }
    
    [ContextMenu("Setup Scene")]
    public void SetupScene()
    {
        Debug.Log("Setting up AI Teammate test scene...");
        
        if (createTerrain)
        {
            CreateBasicTerrain();
        }
        
        CreatePlayer();
        CreateTeammate();
        CreateEnemies();
        
        if (createNavMesh)
        {
            SetupNavMesh();
        }
        
        SetupLighting();

        // Add NavMesh validator
        CreateNavMeshValidator();

        Debug.Log("Scene setup complete!");
    }
    
    void CreateBasicTerrain()
    {
        // Check if terrain already exists
        if (FindObjectOfType<Terrain>() != null)
        {
            Debug.Log("Terrain already exists, skipping creation.");
            return;
        }
        
        // Create a simple plane for testing
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "Ground";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(10f, 1f, 10f); // 100x100 units
        
        // Set up material
        Renderer renderer = ground.GetComponent<Renderer>();
        if (renderer != null)
        {
            renderer.material.color = new Color(0.3f, 0.6f, 0.3f); // Green ground
        }
        
        Debug.Log("Created basic ground plane.");
    }
    
    void CreatePlayer()
    {
        // Check if player already exists
        if (FindObjectOfType<PlayerController>() != null)
        {
            Debug.Log("Player already exists, skipping creation.");
            return;
        }
        
        GameObject player;
        
        if (playerPrefab != null)
        {
            player = Instantiate(playerPrefab, playerSpawnPosition, Quaternion.identity);
        }
        else
        {
            // Create basic player from scratch
            player = new GameObject("Player");
            player.transform.position = playerSpawnPosition;
            
            // Add CharacterController
            CharacterController controller = player.AddComponent<CharacterController>();
            controller.height = 2f;
            controller.radius = 0.5f;
            
            // Add PlayerController script
            player.AddComponent<PlayerController>();
            
            // Add ReviveSystem
            player.AddComponent<ReviveSystem>();
            
            // Create camera
            GameObject cameraObj = new GameObject("PlayerCamera");
            cameraObj.transform.SetParent(player.transform);
            cameraObj.transform.localPosition = new Vector3(0f, 1.6f, 0f);
            
            Camera camera = cameraObj.AddComponent<Camera>();
            camera.tag = "MainCamera";
            
            // Set camera reference in PlayerController
            PlayerController playerController = player.GetComponent<PlayerController>();
            playerController.playerCamera = camera;
            playerController.cameraTransform = cameraObj.transform;
            
            // Add visual representation
            GameObject visual = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            visual.transform.SetParent(player.transform);
            visual.transform.localPosition = Vector3.zero;
            visual.name = "PlayerVisual";
            
            // Remove collider from visual (CharacterController handles collision)
            Destroy(visual.GetComponent<Collider>());
            
            // Set player color
            Renderer playerRenderer = visual.GetComponent<Renderer>();
            if (playerRenderer != null)
            {
                playerRenderer.material.color = Color.blue;
            }
        }
        
        Debug.Log("Created player at " + playerSpawnPosition);
    }
    
    void CreateTeammate()
    {
        // Check if teammate already exists
        if (FindObjectOfType<TeammateAI>() != null)
        {
            Debug.Log("Teammate already exists, skipping creation.");
            return;
        }
        
        GameObject teammate;
        
        if (teammatePrefab != null)
        {
            teammate = Instantiate(teammatePrefab, teammateSpawnPosition, Quaternion.identity);
        }
        else
        {
            // Create basic teammate from scratch
            teammate = new GameObject("Teammate");
            teammate.transform.position = teammateSpawnPosition;
            
            // Add NavMeshAgent
            NavMeshAgent agent = teammate.AddComponent<NavMeshAgent>();
            agent.height = 2f;
            agent.radius = 0.5f;
            
            // Add TeammateAI script
            TeammateAI ai = teammate.AddComponent<TeammateAI>();
            
            // Find and assign player reference
            PlayerController player = FindObjectOfType<PlayerController>();
            if (player != null)
            {
                ai.player = player;
            }
            
            // Add visual representation
            GameObject visual = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            visual.transform.SetParent(teammate.transform);
            visual.transform.localPosition = Vector3.zero;
            visual.name = "TeammateVisual";
            
            // Remove collider from visual (NavMeshAgent handles collision)
            Destroy(visual.GetComponent<Collider>());
            
            // Set teammate color
            Renderer teammateRenderer = visual.GetComponent<Renderer>();
            if (teammateRenderer != null)
            {
                teammateRenderer.material.color = Color.green;
            }
        }
        
        Debug.Log("Created teammate at " + teammateSpawnPosition);
    }
    
    void CreateEnemies()
    {
        // Remove existing enemies if any
        EnemyAI[] existingEnemies = FindObjectsOfType<EnemyAI>();
        foreach (EnemyAI enemy in existingEnemies)
        {
            if (Application.isPlaying)
            {
                Destroy(enemy.gameObject);
            }
            else
            {
                DestroyImmediate(enemy.gameObject);
            }
        }
        
        foreach (Vector3 spawnPos in enemySpawnPositions)
        {
            GameObject enemy;
            
            if (enemyPrefab != null)
            {
                enemy = Instantiate(enemyPrefab, spawnPos, Quaternion.identity);
            }
            else
            {
                // Create basic enemy from scratch
                enemy = new GameObject("Enemy");
                enemy.transform.position = spawnPos;
                enemy.layer = 8; // Enemy layer
                
                // Add NavMeshAgent
                NavMeshAgent agent = enemy.AddComponent<NavMeshAgent>();
                agent.height = 2f;
                agent.radius = 0.5f;
                
                // Add EnemyAI script
                enemy.AddComponent<EnemyAI>();
                
                // Add visual representation
                GameObject visual = GameObject.CreatePrimitive(PrimitiveType.Cube);
                visual.transform.SetParent(enemy.transform);
                visual.transform.localPosition = Vector3.up;
                visual.name = "EnemyVisual";
                
                // Remove collider from visual (NavMeshAgent handles collision)
                Destroy(visual.GetComponent<Collider>());
                
                // Set enemy color
                Renderer enemyRenderer = visual.GetComponent<Renderer>();
                if (enemyRenderer != null)
                {
                    enemyRenderer.material.color = Color.red;
                }
            }
            
            Debug.Log("Created enemy at " + spawnPos);
        }
    }
    
    void SetupNavMesh()
    {
        // This would typically be done through the Navigation window
        // For runtime, we can add NavMeshSurface component if available
        Debug.Log("NavMesh setup: Please bake NavMesh through Window > AI > Navigation");
        Debug.Log("1. Select all ground objects");
        Debug.Log("2. Mark as 'Navigation Static'");
        Debug.Log("3. Go to Navigation window and click 'Bake'");
    }
    
    void SetupLighting()
    {
        // Ensure we have a directional light
        Light[] lights = FindObjectsOfType<Light>();
        bool hasDirectionalLight = false;
        
        foreach (Light light in lights)
        {
            if (light.type == LightType.Directional)
            {
                hasDirectionalLight = true;
                break;
            }
        }
        
        if (!hasDirectionalLight)
        {
            GameObject lightObj = new GameObject("Directional Light");
            Light light = lightObj.AddComponent<Light>();
            light.type = LightType.Directional;
            light.intensity = 1f;
            lightObj.transform.rotation = Quaternion.Euler(50f, -30f, 0f);
            
            Debug.Log("Created directional light.");
        }
    }

    void CreateNavMeshValidator()
    {
        // Check if NavMeshValidator already exists
        if (FindObjectOfType<NavMeshValidator>() != null)
        {
            Debug.Log("NavMeshValidator already exists, skipping creation.");
            return;
        }

        GameObject validatorObj = new GameObject("NavMeshValidator");
        NavMeshValidator validator = validatorObj.AddComponent<NavMeshValidator>();

        // Configure validator
        validator.autoValidateOnStart = true;
        validator.autoFixPositions = true;
        validator.showDebugInfo = true;

        Debug.Log("Created NavMeshValidator for automatic agent validation.");
    }
}
