# Unity AI Teammate Bot

A Unity-based offline AI teammate system that provides intelligent companion behavior for single-player games.

## 🎯 Features

### ✅ Core AI Behaviors
- **Follow Player**: AI maintains optimal distance and follows player movement
- **Revive System**: AI automatically revives downed players
- **Combat AI**: Engages enemies within range with intelligent targeting
- **Loot Collection**: Automatically collects nearby items and resources
- **Party System**: Mock party invite system (ready for multiplayer expansion)

### 🧠 AI State Machine
- **Following**: Maintains distance from player
- **Reviving**: Rushes to revive downed player
- **Combat**: Engages enemies with ranged attacks
- **Looting**: Collects nearby items
- **Idle**: Passive behavior when no tasks needed

## 🛠️ Setup Instructions

### Prerequisites
- Unity 2022.3 LTS or newer
- Unity NavMesh components
- Basic understanding of Unity scripting

### Installation Steps

1. **Create New Unity Project**
   ```
   - Open Unity Hub
   - Create new 3D project
   - Name it "AI Teammate Bot"
   ```

2. **Import Scripts**
   - Copy all scripts from `Assets/Scripts/` to your project
   - Ensure proper folder structure is maintained

3. **Scene Setup**
   ```
   - Create empty scene or use default
   - Add terrain or ground plane
   - Set up NavMesh (Window > AI > Navigation)
   - Bake <PERSON>v<PERSON> for your scene
   ```

4. **Create Prefabs**
   
   **Player Prefab:**
   - Create empty GameObject named "Player"
   - Add CharacterController component
   - Add PlayerController script
   - Add Camera as child object
   - Position camera at head level

   **AI Teammate Prefab:**
   - Create empty GameObject named "Teammate"
   - Add NavMeshAgent component
   - Add TeammateAI script
   - Add visual representation (Capsule primitive)
   - Set layer to default (0)

   **Enemy Prefab:**
   - Create empty GameObject named "Enemy"
   - Add NavMeshAgent component
   - Add EnemyAI script
   - Add visual representation (Cube primitive)
   - Set layer to 8 (Enemy layer)

5. **Layer Setup**
   ```
   Layer 0: Default (Player, Teammate)
   Layer 8: Enemy
   Layer 9: Loot
   ```

6. **Game Manager Setup**
   - Create empty GameObject named "GameManager"
   - Add GameManager script
   - Assign prefab references in inspector
   - Set spawn points if desired

## 🎮 Controls

### Player Controls
- **WASD**: Movement
- **Mouse**: Look around
- **T**: Test damage (development)
- **Y**: Test player down (development)
- **U**: Test party invite (development)
- **ESC**: Quit game

### AI Behaviors (Automatic)
- Follows player when idle
- Rushes to revive when player is downed
- Attacks enemies in range
- Collects nearby loot
- Responds to party invites

## 🔧 Configuration

### TeammateAI Settings
```csharp
followDistance = 3f;        // Distance to maintain from player
maxFollowDistance = 10f;    // Max distance before rushing to player
reviveDistance = 2f;        // Distance needed to revive
reviveTime = 3f;           // Time to complete revive
attackRange = 15f;         // Combat engagement range
lootRange = 5f;           // Loot detection range
```

### Enemy Settings
```csharp
maxHealth = 50f;           // Enemy health points
attackRange = 8f;          // Enemy attack range
damage = 15f;             // Damage per attack
detectionRange = 12f;     // Player detection range
```

## 🧪 Testing

### Basic Testing
1. Play the scene
2. Move around - AI should follow
3. Press 'Y' to go down - AI should revive
4. Enemies should spawn and attack
5. AI should defend and loot

### Advanced Testing
- Test in different terrain types
- Verify NavMesh pathfinding
- Test state transitions
- Verify UI updates correctly

## 🚀 Expansion Ideas

### Immediate Improvements
- Add weapon switching for AI
- Implement AI health system
- Add more loot types
- Improve combat animations

### Advanced Features
- Machine Learning integration (ML-Agents)
- Voice commands for AI
- Multiplayer party system
- Advanced tactical behaviors

### Performance Optimizations
- Object pooling for enemies/loot
- LOD system for distant AI
- Optimized update frequencies
- Spatial partitioning for detection

## 🐛 Troubleshooting

### Common Issues

**AI Not Moving:**
- Check NavMesh is baked
- Verify NavMeshAgent is enabled
- Ensure destination is on NavMesh

**AI Not Following:**
- Check player reference is assigned
- Verify follow distance settings
- Check for obstacles blocking path

**Combat Not Working:**
- Verify layer masks are correct
- Check attack range settings
- Ensure enemies have EnemyAI script

**UI Not Showing:**
- Check Canvas is present
- Verify UI references in GameManager
- Ensure proper UI scaling

### Debug Tips
- Use Debug.Log statements to trace AI state
- Enable Gizmos to visualize ranges
- Use Unity Profiler for performance issues
- Check Console for error messages

## 📝 Code Structure

```
Assets/Scripts/
├── PlayerController.cs    # Player movement and health
├── TeammateAI.cs         # Main AI logic and behaviors
├── EnemyAI.cs           # Enemy behavior and combat
└── GameManager.cs       # Game state and UI management
```

## 🤝 Contributing

Feel free to extend this system with:
- Additional AI behaviors
- New enemy types
- Enhanced UI systems
- Performance improvements

## 📄 License

This project is provided as-is for educational and development purposes.
