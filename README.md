# Unity AI Teammate Bot

A Unity-based offline AI teammate system that provides intelligent companion behavior for single-player games. Built with **Augment Code compatibility** for easy extension and enhancement.

## 🎯 Features

### ✅ Core AI Behaviors
- **Follow Player**: NavMesh-based pathfinding with optimal distance maintenance
- **Revive System**: Dedicated ReviveSystem component with visual/audio feedback
- **Combat AI**: Intelligent enemy targeting with state-based combat
- **Loot Collection**: Automatic item collection with priority system
- **Party System**: Mock party invite system (ready for multiplayer expansion)

### 🧠 AI State Machine
- **Following**: Maintains distance from player using NavMesh
- **Reviving**: Rushes to revive downed player with progress tracking
- **Combat**: Engages enemies with ranged attacks and tactical positioning
- **Looting**: Collects nearby items with smart prioritization
- **Idle**: Passive behavior with occasional look-around

### 🏗️ Clean Architecture
- **Modular Components**: Separate systems for health, revival, AI, and game management
- **Event-Driven**: Loose coupling between player and AI systems
- **Extensible**: Ready for Augment Code enhancements and ML-Agents integration

## 🛠️ Quick Setup Instructions

### Prerequisites
- Unity 2022.3 LTS or newer
- Unity NavMesh components
- Basic understanding of Unity scripting

### 🚀 Automated Setup (Recommended)

1. **Create New Unity Project**
   ```
   - Open Unity Hub
   - Create new 3D project (URP or Built-in)
   - Name it "AI Teammate Bot"
   ```

2. **Import Scripts**
   - Copy all scripts from `Assets/Scripts/` to your project
   - Ensure proper folder structure is maintained

3. **Auto Scene Setup**
   ```
   - Create empty GameObject in scene
   - Add SceneSetup.cs script to it
   - Click "Setup Scene" in context menu OR let it auto-run
   - This creates: Player, Teammate, Enemies, Ground, Lighting
   ```

4. **Bake NavMesh**
   ```
   - Window > AI > Navigation
   - Select ground objects
   - Mark as "Navigation Static"
   - Click "Bake" tab and bake NavMesh
   ```

### 🔧 Manual Setup (Advanced)

**Player Setup:**
- CharacterController + PlayerController + ReviveSystem
- Camera as child (positioned at head level)
- Blue capsule visual representation

**AI Teammate Setup:**
- NavMeshAgent + TeammateAI
- Green capsule visual representation
- Player reference assigned

**Enemy Setup:**
- NavMeshAgent + EnemyAI
- Red cube visual representation
- Layer 8 (Enemy layer)

**Layer Configuration:**
```
Layer 0: Default (Player, Teammate)
Layer 8: Enemy
Layer 9: Loot
```

## 🎮 Controls

### Player Controls
- **WASD**: Movement
- **Mouse**: Look around
- **T**: Test damage (development)
- **Y**: Test player down (development)
- **U**: Test party invite (development)
- **ESC**: Quit game

### AI Behaviors (Automatic)
- Follows player when idle
- Rushes to revive when player is downed
- Attacks enemies in range
- Collects nearby loot
- Responds to party invites

## 🔧 Configuration

### TeammateAI Settings
```csharp
followDistance = 3f;        // Distance to maintain from player
maxFollowDistance = 10f;    // Max distance before rushing to player
reviveDistance = 2f;        // Distance needed to revive
reviveTime = 3f;           // Time to complete revive
attackRange = 15f;         // Combat engagement range
lootRange = 5f;           // Loot detection range
```

### Enemy Settings
```csharp
maxHealth = 50f;           // Enemy health points
attackRange = 8f;          // Enemy attack range
damage = 15f;             // Damage per attack
detectionRange = 12f;     // Player detection range
```

## 🧪 Testing

### Basic Testing
1. Play the scene
2. Move around - AI should follow
3. Press 'Y' to go down - AI should revive
4. Enemies should spawn and attack
5. AI should defend and loot

### Advanced Testing
- Test in different terrain types
- Verify NavMesh pathfinding
- Test state transitions
- Verify UI updates correctly

## 🚀 Expansion Ideas

### Immediate Improvements
- Add weapon switching for AI
- Implement AI health system
- Add more loot types
- Improve combat animations

### Advanced Features
- Machine Learning integration (ML-Agents)
- Voice commands for AI
- Multiplayer party system
- Advanced tactical behaviors

### Performance Optimizations
- Object pooling for enemies/loot
- LOD system for distant AI
- Optimized update frequencies
- Spatial partitioning for detection

## 🐛 Troubleshooting

### Common Issues

**AI Not Moving:**
- Check NavMesh is baked
- Verify NavMeshAgent is enabled
- Ensure destination is on NavMesh

**AI Not Following:**
- Check player reference is assigned
- Verify follow distance settings
- Check for obstacles blocking path

**Combat Not Working:**
- Verify layer masks are correct
- Check attack range settings
- Ensure enemies have EnemyAI script

**UI Not Showing:**
- Check Canvas is present
- Verify UI references in GameManager
- Ensure proper UI scaling

### Debug Tips
- Use Debug.Log statements to trace AI state
- Enable Gizmos to visualize ranges
- Use Unity Profiler for performance issues
- Check Console for error messages

## 📝 Code Structure

```
Assets/Scripts/
├── PlayerController.cs    # Player movement and health
├── ReviveSystem.cs       # Dedicated revive/death system
├── TeammateAI.cs         # Main AI logic and behaviors
├── EnemyAI.cs           # Enemy behavior and combat
├── GameManager.cs       # Game state and UI management
└── SceneSetup.cs        # Automated scene setup helper
```

### 🧩 Component Architecture

**PlayerController.cs**
- Movement with CharacterController
- Health management
- Integration with ReviveSystem
- Event system for AI communication

**ReviveSystem.cs**
- Downed state management
- Revival mechanics with progress tracking
- Visual/audio feedback system
- Death and respawn handling

**TeammateAI.cs**
- NavMesh-based pathfinding
- State machine (Following, Reviving, Combat, Looting, Idle)
- Priority-based decision making
- Event-driven player interaction

**EnemyAI.cs**
- Simple enemy behavior for testing
- Target detection and pursuit
- Basic combat mechanics
- Loot dropping on death

## 🤝 Contributing

Feel free to extend this system with:
- Additional AI behaviors
- New enemy types
- Enhanced UI systems
- Performance improvements

## 📄 License

This project is provided as-is for educational and development purposes.
