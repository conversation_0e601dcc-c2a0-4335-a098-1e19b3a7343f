using UnityEngine;

public class ReviveSystem : MonoBehaviour
{
    [Header("Revive Settings")]
    public bool isDown = false;
    public float maxDownedTime = 30f; // Time before permanent death
    public float downedTime = 0f;
    
    [Header("Visual Effects")]
    public GameObject downedEffect;
    public GameObject reviveEffect;
    public ParticleSystem downedParticles;
    
    [Header("Audio")]
    public AudioClip downedSound;
    public AudioClip reviveSound;
    
    private AudioSource audioSource;
    private bool permanentlyDead = false;
    
    // Events
    public System.Action OnPlayerDowned;
    public System.Action OnPlayerRevived;
    public System.Action OnPlayerDied;
    
    void Start()
    {
        // Get or add AudioSource
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
        }
        
        // Set up downed particles if not assigned
        if (downedParticles == null)
        {
            // Create a simple particle system for downed state
            GameObject particleObj = new GameObject("DownedParticles");
            particleObj.transform.SetParent(transform);
            particleObj.transform.localPosition = Vector3.up;
            
            downedParticles = particleObj.AddComponent<ParticleSystem>();
            var main = downedParticles.main;
            main.startColor = Color.red;
            main.startSize = 0.1f;
            main.startLifetime = 2f;
            main.maxParticles = 20;
            
            var emission = downedParticles.emission;
            emission.rateOverTime = 10f;
            
            var shape = downedParticles.shape;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = 0.5f;
            
            downedParticles.Stop();
        }
    }
    
    void Update()
    {
        if (isDown && !permanentlyDead)
        {
            downedTime += Time.deltaTime;
            
            if (downedTime >= maxDownedTime)
            {
                Die();
            }
        }
    }
    
    public void Down()
    {
        if (isDown || permanentlyDead) return;
        
        isDown = true;
        downedTime = 0f;
        
        // Visual effects
        if (downedEffect != null)
        {
            GameObject effect = Instantiate(downedEffect, transform.position, Quaternion.identity);
            Destroy(effect, 3f); // Clean up after 3 seconds
        }
        
        if (downedParticles != null)
        {
            downedParticles.Play();
        }
        
        // Audio
        if (downedSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(downedSound);
        }
        
        // Events
        OnPlayerDowned?.Invoke();
        
        Debug.Log("Player is down! Waiting for revive...");
    }
    
    public void Revive()
    {
        if (!isDown || permanentlyDead) return;
        
        isDown = false;
        downedTime = 0f;
        
        // Visual effects
        if (reviveEffect != null)
        {
            GameObject effect = Instantiate(reviveEffect, transform.position, Quaternion.identity);
            Destroy(effect, 3f); // Clean up after 3 seconds
        }
        
        if (downedParticles != null)
        {
            downedParticles.Stop();
        }
        
        // Audio
        if (reviveSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(reviveSound);
        }
        
        // Events
        OnPlayerRevived?.Invoke();
        
        Debug.Log("Player has been revived!");
    }
    
    public void Die()
    {
        if (permanentlyDead) return;
        
        permanentlyDead = true;
        isDown = false; // No longer down, now dead
        
        if (downedParticles != null)
        {
            downedParticles.Stop();
        }
        
        // Events
        OnPlayerDied?.Invoke();
        
        Debug.Log("Player has died permanently!");
        
        // Handle permanent death (restart level, game over screen, etc.)
        // For now, just disable the player
        StartCoroutine(HandleDeath());
    }
    
    private System.Collections.IEnumerator HandleDeath()
    {
        yield return new WaitForSeconds(2f); // Wait 2 seconds before respawn/game over
        
        // Option 1: Respawn player
        Respawn();
        
        // Option 2: Game over (uncomment if preferred)
        // gameObject.SetActive(false);
        // GameManager.Instance?.GameOver();
    }
    
    public void Respawn()
    {
        permanentlyDead = false;
        isDown = false;
        downedTime = 0f;
        
        // Reset player health
        PlayerController player = GetComponent<PlayerController>();
        if (player != null)
        {
            player.currentHealth = player.maxHealth;
        }
        
        Debug.Log("Player respawned!");
    }
    
    // Utility methods
    public bool IsDown() => isDown;
    public bool IsDead() => permanentlyDead;
    public float GetDownedTimeRemaining() => Mathf.Max(0f, maxDownedTime - downedTime);
    public float GetDownedProgress() => downedTime / maxDownedTime;
    
    // Method for AI to check if player needs reviving
    public bool NeedsRevive() => isDown && !permanentlyDead;
    
    // Method to force instant death (for testing or special scenarios)
    public void ForceKill()
    {
        if (!permanentlyDead)
        {
            downedTime = maxDownedTime;
            Die();
        }
    }
    
    void OnDrawGizmosSelected()
    {
        if (isDown)
        {
            // Draw a red sphere to indicate downed state
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position + Vector3.up, 1f);
        }
        
        if (permanentlyDead)
        {
            // Draw a black sphere to indicate death
            Gizmos.color = Color.black;
            Gizmos.DrawWireSphere(transform.position + Vector3.up, 1.5f);
        }
    }
}
