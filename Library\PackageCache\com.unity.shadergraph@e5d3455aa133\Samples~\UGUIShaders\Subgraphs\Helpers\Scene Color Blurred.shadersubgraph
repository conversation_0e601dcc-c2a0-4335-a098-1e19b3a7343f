{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "cb88cccf121b4ddfa8e5edf4384961ea",
    "m_Properties": [
        {
            "m_Id": "ff6c1a9c0487403e825804e84a165ced"
        },
        {
            "m_Id": "5112b74a42c74774877d9caed4877f96"
        },
        {
            "m_Id": "3d22fccbc87e4fc79cb191efcdc97052"
        },
        {
            "m_Id": "230b0011e7bc4fe58f0b4a6a30fb5a2c"
        }
    ],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "de75195c6be64acbbbc7c8af0873e4eb"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "53664f95d8e443f6a5fcc7712cdba551"
        },
        {
            "m_Id": "1a8f5730f4b2425aa252e141aacca6ba"
        },
        {
            "m_Id": "aabd07734129467b94b3983667bfe3ad"
        },
        {
            "m_Id": "457966b5cf744fa7a7b8e2ade3c7fcbd"
        },
        {
            "m_Id": "8ddf146e6b174c2f99521adbcdf674c1"
        },
        {
            "m_Id": "3b407ca4de794e83aa320e35d66e6187"
        },
        {
            "m_Id": "c7aecbe5f64d4fdc8adfb33996ed4a67"
        },
        {
            "m_Id": "8ad6c74ea09a4efda168557e42f2b343"
        },
        {
            "m_Id": "ea7e92bc22394e9c82eeb0a7f399639a"
        },
        {
            "m_Id": "998892cf26734b919ba9542ffe6cf90e"
        },
        {
            "m_Id": "737b1e9970fd41ae924bb8ef70f357c8"
        },
        {
            "m_Id": "bc0ffa015c204b7d814d67af14f89115"
        },
        {
            "m_Id": "a1da500ba30b4003b09a72a4f2f3b783"
        },
        {
            "m_Id": "7c5361a9e04542838dfa44ecde3cb0a7"
        },
        {
            "m_Id": "f38268c1ade6429c9abdc6329e312322"
        },
        {
            "m_Id": "a397feef18ff44ffa83479d0c9d8f5f6"
        },
        {
            "m_Id": "55762238327748b6af5014e58dacbd21"
        },
        {
            "m_Id": "327d750b28a145938b0187e57c7f28ca"
        },
        {
            "m_Id": "8c0bc1c02394474c87b8fdc3837ee793"
        },
        {
            "m_Id": "d2ee2d84e52740debe807f63eb40e5ec"
        },
        {
            "m_Id": "7a3d5cdbb4eb4b18a4249b4930828c0e"
        },
        {
            "m_Id": "2715019fc6c7411ea77df20fd8466373"
        },
        {
            "m_Id": "450baa23e63f462395e9d768b16c2497"
        },
        {
            "m_Id": "8bb03731cc2d4e83b1e369eddfe9dffe"
        },
        {
            "m_Id": "09a10b62240c4d76a9a617fddfb5dbf2"
        },
        {
            "m_Id": "989c719ded474574ab9b04ec317d402c"
        },
        {
            "m_Id": "a1e78ce4836549e287123d4742413065"
        },
        {
            "m_Id": "efd4ad4c478b46a985f4bedf9d8652f0"
        },
        {
            "m_Id": "317b3ccb2a8645a5bab5af8cdbc9141d"
        },
        {
            "m_Id": "66e72937df9044478beae91f6b083144"
        },
        {
            "m_Id": "059c5d87a1b7461eadd6874afe75b071"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "8aee714049c54bd18f6675b4233b60cb"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "bafad5a2077b4902b40c519abda57487"
        },
        {
            "m_Id": "513ef1fa7a3c480db1d2a7b4dfd3c13d"
        },
        {
            "m_Id": "7137de976c75482591208010f1587c4b"
        },
        {
            "m_Id": "14867dd86b394bf2864b5bb3c55a4b68"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "059c5d87a1b7461eadd6874afe75b071"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1a8f5730f4b2425aa252e141aacca6ba"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "09a10b62240c4d76a9a617fddfb5dbf2"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "989c719ded474574ab9b04ec317d402c"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "1a8f5730f4b2425aa252e141aacca6ba"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "a397feef18ff44ffa83479d0c9d8f5f6"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "2715019fc6c7411ea77df20fd8466373"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7a3d5cdbb4eb4b18a4249b4930828c0e"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "317b3ccb2a8645a5bab5af8cdbc9141d"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "2715019fc6c7411ea77df20fd8466373"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "327d750b28a145938b0187e57c7f28ca"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "d2ee2d84e52740debe807f63eb40e5ec"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "3b407ca4de794e83aa320e35d66e6187"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1a8f5730f4b2425aa252e141aacca6ba"
                },
                "m_SlotId": 3
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "450baa23e63f462395e9d768b16c2497"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "8bb03731cc2d4e83b1e369eddfe9dffe"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "450baa23e63f462395e9d768b16c2497"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "8bb03731cc2d4e83b1e369eddfe9dffe"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "457966b5cf744fa7a7b8e2ade3c7fcbd"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "059c5d87a1b7461eadd6874afe75b071"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "55762238327748b6af5014e58dacbd21"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "a397feef18ff44ffa83479d0c9d8f5f6"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "66e72937df9044478beae91f6b083144"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "317b3ccb2a8645a5bab5af8cdbc9141d"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "737b1e9970fd41ae924bb8ef70f357c8"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "bc0ffa015c204b7d814d67af14f89115"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "7a3d5cdbb4eb4b18a4249b4930828c0e"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "a1e78ce4836549e287123d4742413065"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "7c5361a9e04542838dfa44ecde3cb0a7"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1a8f5730f4b2425aa252e141aacca6ba"
                },
                "m_SlotId": 11
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "8ad6c74ea09a4efda168557e42f2b343"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "bc0ffa015c204b7d814d67af14f89115"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "8bb03731cc2d4e83b1e369eddfe9dffe"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "09a10b62240c4d76a9a617fddfb5dbf2"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "8c0bc1c02394474c87b8fdc3837ee793"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "d2ee2d84e52740debe807f63eb40e5ec"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "8ddf146e6b174c2f99521adbcdf674c1"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1a8f5730f4b2425aa252e141aacca6ba"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "989c719ded474574ab9b04ec317d402c"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "66e72937df9044478beae91f6b083144"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "998892cf26734b919ba9542ffe6cf90e"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "737b1e9970fd41ae924bb8ef70f357c8"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "998892cf26734b919ba9542ffe6cf90e"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "f38268c1ade6429c9abdc6329e312322"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "a1da500ba30b4003b09a72a4f2f3b783"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1a8f5730f4b2425aa252e141aacca6ba"
                },
                "m_SlotId": 10
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "a1e78ce4836549e287123d4742413065"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "a397feef18ff44ffa83479d0c9d8f5f6"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "a397feef18ff44ffa83479d0c9d8f5f6"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "53664f95d8e443f6a5fcc7712cdba551"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "aabd07734129467b94b3983667bfe3ad"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1a8f5730f4b2425aa252e141aacca6ba"
                },
                "m_SlotId": 9
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "bc0ffa015c204b7d814d67af14f89115"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "a1da500ba30b4003b09a72a4f2f3b783"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "c7aecbe5f64d4fdc8adfb33996ed4a67"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1a8f5730f4b2425aa252e141aacca6ba"
                },
                "m_SlotId": 5
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "d2ee2d84e52740debe807f63eb40e5ec"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7a3d5cdbb4eb4b18a4249b4930828c0e"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "ea7e92bc22394e9c82eeb0a7f399639a"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "998892cf26734b919ba9542ffe6cf90e"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "ea7e92bc22394e9c82eeb0a7f399639a"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "998892cf26734b919ba9542ffe6cf90e"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "efd4ad4c478b46a985f4bedf9d8652f0"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "a1e78ce4836549e287123d4742413065"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "f38268c1ade6429c9abdc6329e312322"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1a8f5730f4b2425aa252e141aacca6ba"
                },
                "m_SlotId": 13
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Procedural/Helpers",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 1,
    "m_OutputNode": {
        "m_Id": "53664f95d8e443f6a5fcc7712cdba551"
    },
    "m_SubDatas": [],
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "028f9b53abcd4d5cbc13754a313ece22",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "059c5d87a1b7461eadd6874afe75b071",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -415.5000305175781,
            "y": 12.000001907348633,
            "width": 126.0,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "f613324aa7ed45e79a8cf57d74171dcd"
        },
        {
            "m_Id": "8457ebc0b34742398f05f5be56405820"
        },
        {
            "m_Id": "4eddb5d449d94281a34a55fe12e402ea"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "088f301ae30e468f823e2aed0ec8b3e6",
    "m_Id": 1,
    "m_DisplayName": "True",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "True",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.LogNode",
    "m_ObjectId": "09a10b62240c4d76a9a617fddfb5dbf2",
    "m_Group": {
        "m_Id": "8aee714049c54bd18f6675b4233b60cb"
    },
    "m_Name": "Log",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1101.0,
            "y": 825.5,
            "width": 145.0,
            "height": 128.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "1a01ba824110411888ef3e334128ffd5"
        },
        {
            "m_Id": "7dc4fc0a4a014c379dbfd0ab0caf9fce"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_LogBase": 1
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "14867dd86b394bf2864b5bb3c55a4b68",
    "m_Title": "Scene Color Blurred",
    "m_Content": "Samples the opaque pass (behind the user interface) multiple times in a spiral shape to create a blurred version of the background.\n\nIn URP, this is an expensive opperation because of the number of samples. Higher Cycles and Samples Per Cycle create a higher quality result but also cost a lot more.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -591.5,
        "y": -495.5,
        "width": 224.5,
        "height": 175.5
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "16fdf1fc68264f31b684bbef2b9c4f37",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "18a249a1509341359bb4838db5b8fc53",
    "m_Id": 2,
    "m_DisplayName": "Y",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Y",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "Y"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "1a01ba824110411888ef3e334128ffd5",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "1a8f5730f4b2425aa252e141aacca6ba",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SpiralBlurCustomFunction (Custom Function)",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -271.5000305175781,
            "y": -60.0,
            "width": 301.9999694824219,
            "height": 334.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "268987c725d04f7f8daa8515ed1c9dd1"
        },
        {
            "m_Id": "40483a3fa8af41d38e5a66ba01180146"
        },
        {
            "m_Id": "7174db7d34fb437fae514b2bf19b28e5"
        },
        {
            "m_Id": "830d410514734a3fa7fe12a9c4dfcede"
        },
        {
            "m_Id": "e9de79cd2fa944f3a8f7f5e134abd88e"
        },
        {
            "m_Id": "2bd2dec51d574aaf82bf59a44e6ba3fe"
        },
        {
            "m_Id": "84b980c3c73e4224b7fdbc13fa04e8fa"
        },
        {
            "m_Id": "caa04c7d85624e7ba41023bb5d598dc4"
        },
        {
            "m_Id": "6a6e6ab118b3472e82f2dffd39cb97e5"
        },
        {
            "m_Id": "c31a9639803d49c9b9c81dd4733fb535"
        }
    ],
    "synonyms": [
        "code",
        "HLSL"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 1,
    "m_FunctionName": "SpiralBlurCustomFunction",
    "m_FunctionSource": "",
    "m_FunctionSourceUsePragmas": true,
    "m_FunctionBody": "float2 NextUV = ScreenPosition;\nfloat2 WidthHeightRatio = float2(SceneSize.y/SceneSize.x,1);\r\nfloat StepSize = Blurriness / (int) Cycles;\r\nfloat CurDistance=0;\r\nfloat2 CurOffset;\r\r\nfloat Substep;\nfloat Noise = SAMPLE_TEXTURE2D(Tex.tex,Tex.samplerstate,ScenePixels).w *2-1;\n\r\r\nif (Cycles<1)\r\n{\r\n\tOut = SHADERGRAPH_SAMPLE_SCENE_COLOR(NextUV).xyz;\r\n}\r\nelse\r\n{\r\n\tfor (int i = 0; i < (int) Cycles; i++)\r\n\t{\r\n\t\tfor (int j = 0; j < (int) SamplesPerCycle; j++)\r\n\t\t{\r\n\t\t\tsincos(6.283185*((Noise+Substep) / SamplesPerCycle), CurOffset.y, CurOffset.x);\r\n\t\t\tCurOffset *=BlurMultiplier;\n                              NextUV = ScreenPosition + (CurOffset * (CurDistance+(Noise*StepSize))* WidthHeightRatio);\r\r\n\t\t\tOut += SHADERGRAPH_SAMPLE_SCENE_COLOR(NextUV).xyz;\r\n\t\t\tSubstep++;\r\n\t\t}\r\n\t\tCurDistance+=StepSize;\r\n\t\tSubstep+=0.618;\r\r\n\t}\r\n\tOut = Out / ((int)Cycles*(int)SamplesPerCycle);\r\n}"
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "230b0011e7bc4fe58f0b4a6a30fb5a2c",
    "m_Guid": {
        "m_GuidSerialized": "ed96582c-1dc8-47cd-b399-c49156b7b022"
    },
    "m_Name": "Blur Multiplier",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Blur Multiplier",
    "m_DefaultReferenceName": "_Blur_Multiplier",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 1.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "237b9e969905404c8c874f534b854966",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BooleanMaterialSlot",
    "m_ObjectId": "2656b89ff90b4a378a2c374999c04c4f",
    "m_Id": 0,
    "m_DisplayName": "URP",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "URP",
    "m_StageCapability": 3,
    "m_Value": false,
    "m_DefaultValue": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "268987c725d04f7f8daa8515ed1c9dd1",
    "m_Id": 9,
    "m_DisplayName": "ScreenPosition",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "ScreenPosition",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2Node",
    "m_ObjectId": "2715019fc6c7411ea77df20fd8466373",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Vector 2",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -555.5,
            "y": 675.0,
            "width": 126.99996948242188,
            "height": 101.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "930bc07798e54bc6b52ef7ddf797de82"
        },
        {
            "m_Id": "18a249a1509341359bb4838db5b8fc53"
        },
        {
            "m_Id": "7a392a53de464785948901087c2aa330"
        }
    ],
    "synonyms": [
        "2",
        "v2",
        "vec2",
        "float2"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BooleanMaterialSlot",
    "m_ObjectId": "29359f5b01ae49cf93a2b3dba98baf8b",
    "m_Id": 0,
    "m_DisplayName": "Predicate",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Predicate",
    "m_StageCapability": 3,
    "m_Value": false,
    "m_DefaultValue": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2a4811c1c2124e4e8166a5347e968e37",
    "m_Id": 0,
    "m_DisplayName": "Samples Per Cycle",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2bd2dec51d574aaf82bf59a44e6ba3fe",
    "m_Id": 3,
    "m_DisplayName": "SamplesPerCycle",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "SamplesPerCycle",
    "m_StageCapability": 3,
    "m_Value": 8.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "2ca469e74a1c471d80b65e28fb4db397",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MaximumNode",
    "m_ObjectId": "317b3ccb2a8645a5bab5af8cdbc9141d",
    "m_Group": {
        "m_Id": "8aee714049c54bd18f6675b4233b60cb"
    },
    "m_Name": "Maximum",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -702.5,
            "y": 825.5,
            "width": 126.0,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "842d441d72f444209d24ead1c452ac3d"
        },
        {
            "m_Id": "76bc7f4a59c64fa28143ae0cac76c2cc"
        },
        {
            "m_Id": "4caa6bc1e2064aba8233fda489242d39"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "327d750b28a145938b0187e57c7f28ca",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -793.0000610351563,
            "y": 561.0,
            "width": 126.50006103515625,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "4d5c67f7226b4983805f0eeb1621839b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "ff6c1a9c0487403e825804e84a165ced"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "3b407ca4de794e83aa320e35d66e6187",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -595.9999389648438,
            "y": 120.0,
            "width": 172.49996948242188,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "2a4811c1c2124e4e8166a5347e968e37"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "3d22fccbc87e4fc79cb191efcdc97052"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3b87ec02601042679bbbb31a92e5e317",
    "m_Id": 1,
    "m_DisplayName": "Height",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Height",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "3d22fccbc87e4fc79cb191efcdc97052",
    "m_Guid": {
        "m_GuidSerialized": "7647d0b1-660a-474e-8ffa-c6826af9eff1"
    },
    "m_Name": "Samples Per Cycle",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Samples Per Cycle",
    "m_DefaultReferenceName": "_Samples_Per_Cycle",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 2.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "3d535ce66dd046bc8f5d57857b536c25",
    "m_Id": 2,
    "m_DisplayName": "Out Min Max",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "OutMinMax",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 1.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "40483a3fa8af41d38e5a66ba01180146",
    "m_Id": 10,
    "m_DisplayName": "ScenePixels",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "ScenePixels",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "446c286520cb4e5a8597117d5e831a49",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "448293ace8884efcaedb8b9a0dc921e3",
    "m_Id": 0,
    "m_DisplayName": "Cycles",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "44d9b3c8e5d54d1f884f94fd98188c8b",
    "m_Id": 1,
    "m_DisplayName": "BlurredScene",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BlurredScene",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ScreenNode",
    "m_ObjectId": "450baa23e63f462395e9d768b16c2497",
    "m_Group": {
        "m_Id": "8aee714049c54bd18f6675b4233b60cb"
    },
    "m_Name": "Screen",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1314.5,
            "y": 825.5,
            "width": 87.5,
            "height": 101.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "7cd6557318ec434f97c5f638315c2238"
        },
        {
            "m_Id": "ef0acb5056e7424ebd36a8e78adabf58"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "457966b5cf744fa7a7b8e2ade3c7fcbd",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -555.9999389648438,
            "y": 51.999996185302737,
            "width": 126.49996948242188,
            "height": 33.999996185302737
        }
    },
    "m_Slots": [
        {
            "m_Id": "b2b7575a1ef0441f929c59c91d8cc13a"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "ff6c1a9c0487403e825804e84a165ced"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "47c1871bbbb84e489082ac14f804f029",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "4caa6bc1e2064aba8233fda489242d39",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "4d5c67f7226b4983805f0eeb1621839b",
    "m_Id": 0,
    "m_DisplayName": "Blurriness",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "4eddb5d449d94281a34a55fe12e402ea",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "505b98c736a94009adb5b0f79795200a",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 3.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "5112b74a42c74774877d9caed4877f96",
    "m_Guid": {
        "m_GuidSerialized": "056ed38d-d7ca-4c33-b49f-6de25a5075e3"
    },
    "m_Name": "Cycles",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Cycles",
    "m_DefaultReferenceName": "_Cycles",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 3.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "513ef1fa7a3c480db1d2a7b4dfd3c13d",
    "m_Title": "",
    "m_Content": "Generates Scene Color samples in a spiral pattern out from the center sample and then averages them together. Samples are randomly offset using the noise texture input.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -225.00001525878907,
        "y": -178.50001525878907,
        "width": 200.00001525878907,
        "height": 100.50001525878906
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "53664f95d8e443f6a5fcc7712cdba551",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Output",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 336.5,
            "y": -60.0000114440918,
            "width": 125.0,
            "height": 77.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "44d9b3c8e5d54d1f884f94fd98188c8b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "55762238327748b6af5014e58dacbd21",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "CurrentActiveRP (Custom Function)",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -243.4999542236328,
            "y": -311.9999694824219,
            "width": 247.99998474121095,
            "height": 93.99996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "2656b89ff90b4a378a2c374999c04c4f"
        },
        {
            "m_Id": "fd7d7f7e93984b598100e1b2bba60ae1"
        }
    ],
    "synonyms": [
        "code",
        "HLSL"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 1,
    "m_FunctionName": "CurrentActiveRP",
    "m_FunctionSource": "",
    "m_FunctionSourceUsePragmas": true,
    "m_FunctionBody": "URP = false;\nHDRP = false;\n\n#if SHADEROPTIONS_PRE_EXPOSITION //#if defined(UNITY_HEADER_HD_INCLUDED)\nHDRP = true;\n\r\n#elif defined(UNIVERSAL_PIPELINE_CORE_INCLUDED)\nURP = true;\n\n#endif"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "55d0f6d333314b3f8fb83f46f23aa5fb",
    "m_Id": 1,
    "m_DisplayName": "",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "5783c3c0c5944f7481eb3ba0a25ac62e",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "5cf6af3ff6eb4697b01b02973efa0e80",
    "m_Id": 3,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "652d673d78cf4df9a3e4ebeefb1e154d",
    "m_Id": 1,
    "m_DisplayName": "lod",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "lod",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubtractNode",
    "m_ObjectId": "66e72937df9044478beae91f6b083144",
    "m_Group": {
        "m_Id": "8aee714049c54bd18f6675b4233b60cb"
    },
    "m_Name": "Subtract",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -828.5,
            "y": 825.5,
            "width": 126.0,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "68336ce00c204a7c87378d361a8173b6"
        },
        {
            "m_Id": "505b98c736a94009adb5b0f79795200a"
        },
        {
            "m_Id": "c23e111cd93f47c1a858f660f676bce0"
        }
    ],
    "synonyms": [
        "subtraction",
        "remove",
        "minus",
        "take away"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "68336ce00c204a7c87378d361a8173b6",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "6a6e6ab118b3472e82f2dffd39cb97e5",
    "m_Id": 11,
    "m_DisplayName": "Hack",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Hack",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "6d3812b18ecf4e4884f8b4698c39dae8",
    "m_Id": 2,
    "m_DisplayName": "False",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "False",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "70cf0785009042edb0c960feb335466a",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "7137de976c75482591208010f1587c4b",
    "m_Title": "",
    "m_Content": "This \"Hack\" is here so that the #define REQUIRE_OPAQUE_TEXTURE flag will get added to the shader.  I don't think there is another way for a subgraph to do that.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -299.0000305175781,
        "y": 213.50001525878907,
        "width": 131.00001525878907,
        "height": 131.50001525878907
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "7174db7d34fb437fae514b2bf19b28e5",
    "m_Id": 13,
    "m_DisplayName": "SceneSize",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "SceneSize",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DivideNode",
    "m_ObjectId": "737b1e9970fd41ae924bb8ef70f357c8",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Divide",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -806.0000610351563,
            "y": -205.49998474121095,
            "width": 129.0,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "028f9b53abcd4d5cbc13754a313ece22"
        },
        {
            "m_Id": "de0e5fcecbeb4604a2d79e7bf60c1c8f"
        },
        {
            "m_Id": "a34d1580a85b446fbf2ccdc2f5605a9f"
        }
    ],
    "synonyms": [
        "division",
        "divided by"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "76bc7f4a59c64fa28143ae0cac76c2cc",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "7727f5695e464c7689f373681b745505",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "79a889584c5e425ea370fb0043e269a0",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": -1.0,
        "y": -1.0,
        "z": -1.0,
        "w": -1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "7a392a53de464785948901087c2aa330",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.RemapNode",
    "m_ObjectId": "7a3d5cdbb4eb4b18a4249b4930828c0e",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Remap",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -394.0000305175781,
            "y": 573.5,
            "width": 185.49998474121095,
            "height": 142.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "79a889584c5e425ea370fb0043e269a0"
        },
        {
            "m_Id": "9a5850e532124ffe986febc352f0087f"
        },
        {
            "m_Id": "3d535ce66dd046bc8f5d57857b536c25"
        },
        {
            "m_Id": "c704b982405e4087832086c1d6b915a0"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SceneColorNode",
    "m_ObjectId": "7c5361a9e04542838dfa44ecde3cb0a7",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Scene Color",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -461.0,
            "y": 180.50001525878907,
            "width": 138.0,
            "height": 76.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "ac3e787696d9475692a17934439d165a"
        },
        {
            "m_Id": "cb3b780941634c379027b79a032b6e08"
        }
    ],
    "synonyms": [
        "screen buffer"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7cd6557318ec434f97c5f638315c2238",
    "m_Id": 0,
    "m_DisplayName": "Width",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Width",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "7dc4fc0a4a014c379dbfd0ab0caf9fce",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "806489e0095d48b2ad4686b1da26fbe0",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "830d410514734a3fa7fe12a9c4dfcede",
    "m_Id": 1,
    "m_DisplayName": "Blurriness",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Blurriness",
    "m_StageCapability": 3,
    "m_Value": 0.05000000074505806,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "842d441d72f444209d24ead1c452ac3d",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "8457ebc0b34742398f05f5be56405820",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.5,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "84b980c3c73e4224b7fdbc13fa04e8fa",
    "m_Id": 5,
    "m_DisplayName": "BlurMultiplier",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BlurMultiplier",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "88579388cd2f4121aa483d074153ac26",
    "m_Id": 0,
    "m_DisplayName": "Width",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Width",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ScreenPositionNode",
    "m_ObjectId": "8ad6c74ea09a4efda168557e42f2b343",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Screen Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -848.5000610351563,
            "y": -26.499980926513673,
            "width": 145.0,
            "height": 128.50001525878907
        }
    },
    "m_Slots": [
        {
            "m_Id": "70cf0785009042edb0c960feb335466a"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_ScreenSpaceType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "8aee714049c54bd18f6675b4233b60cb",
    "m_Title": "Calculate the max number of screen mips",
    "m_Position": {
        "x": -1340.000244140625,
        "y": 766.6668701171875
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MaximumNode",
    "m_ObjectId": "8bb03731cc2d4e83b1e369eddfe9dffe",
    "m_Group": {
        "m_Id": "8aee714049c54bd18f6675b4233b60cb"
    },
    "m_Name": "Maximum",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1227.0,
            "y": 825.5,
            "width": 126.0,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "806489e0095d48b2ad4686b1da26fbe0"
        },
        {
            "m_Id": "c79fab84e0454c9b921c555de4974552"
        },
        {
            "m_Id": "2ca469e74a1c471d80b65e28fb4db397"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "8c0bc1c02394474c87b8fdc3837ee793",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -810.0000610351563,
            "y": 595.0,
            "width": 143.50006103515626,
            "height": 34.00006103515625
        }
    },
    "m_Slots": [
        {
            "m_Id": "de399a0f648440a5b6a764b134f35987"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "230b0011e7bc4fe58f0b4a6a30fb5a2c"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "8c9bb6e32d5e49d5a5f5b8ab7bb2bcb1",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 2.0,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "8ddf146e6b174c2f99521adbcdf674c1",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -539.9999389648438,
            "y": 85.99999237060547,
            "width": 109.99996948242188,
            "height": 34.00000762939453
        }
    },
    "m_Slots": [
        {
            "m_Id": "448293ace8884efcaedb8b9a0dc921e3"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "5112b74a42c74774877d9caed4877f96"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "8f99f7b62ef542938f13ac67091ca438",
    "m_Id": 3,
    "m_DisplayName": "finalColor",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "finalColor",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BooleanMaterialSlot",
    "m_ObjectId": "91f7f2629d2b4684b866147de909eefb",
    "m_Id": 2,
    "m_DisplayName": "exposureIsOn",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "exposureIsOn",
    "m_StageCapability": 3,
    "m_Value": true,
    "m_DefaultValue": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "930bc07798e54bc6b52ef7ddf797de82",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "96c94dd03b144bdca554a0263d52d345",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.FloorNode",
    "m_ObjectId": "989c719ded474574ab9b04ec317d402c",
    "m_Group": {
        "m_Id": "8aee714049c54bd18f6675b4233b60cb"
    },
    "m_Name": "Floor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -956.0,
            "y": 825.5,
            "width": 127.5,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "e48338b1e2614c2497cc7020ef18158d"
        },
        {
            "m_Id": "96c94dd03b144bdca554a0263d52d345"
        }
    ],
    "synonyms": [
        "down"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2Node",
    "m_ObjectId": "998892cf26734b919ba9542ffe6cf90e",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Vector 2",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -959.5000610351563,
            "y": -127.50001525878906,
            "width": 127.0,
            "height": 101.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "5783c3c0c5944f7481eb3ba0a25ac62e"
        },
        {
            "m_Id": "e1b0c4b0cdde40248b3f2af3ac7d276c"
        },
        {
            "m_Id": "b03f8865c0d441c48a0cd9b9a2859041"
        }
    ],
    "synonyms": [
        "2",
        "v2",
        "vec2",
        "float2"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "9a5850e532124ffe986febc352f0087f",
    "m_Id": 1,
    "m_DisplayName": "In Min Max",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "InMinMax",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0
    },
    "m_DefaultValue": {
        "x": -1.0,
        "y": 1.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.FractionNode",
    "m_ObjectId": "a1da500ba30b4003b09a72a4f2f3b783",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Fraction",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -522.5000610351563,
            "y": -127.50001525878906,
            "width": 130.50003051757813,
            "height": 94.00000762939453
        }
    },
    "m_Slots": [
        {
            "m_Id": "d67ab90ed7344299932b65b44821b186"
        },
        {
            "m_Id": "e66bc2fcbccb4b8da5fbcd5b85f041fe"
        }
    ],
    "synonyms": [
        "remainder"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "a1e78ce4836549e287123d4742413065",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SafeHDSceneColor (Custom Function)",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -150.00003051757813,
            "y": 502.5,
            "width": 263.0000915527344,
            "height": 142.00006103515626
        }
    },
    "m_Slots": [
        {
            "m_Id": "47c1871bbbb84e489082ac14f804f029"
        },
        {
            "m_Id": "652d673d78cf4df9a3e4ebeefb1e154d"
        },
        {
            "m_Id": "91f7f2629d2b4684b866147de909eefb"
        },
        {
            "m_Id": "8f99f7b62ef542938f13ac67091ca438"
        }
    ],
    "synonyms": [
        "code",
        "HLSL"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 0,
    "m_FunctionName": "SafeHDSceneColor",
    "m_FunctionSource": "5bb1a469682cd4244aa48b5d6af7ddad",
    "m_FunctionSourceUsePragmas": true,
    "m_FunctionBody": "Enter function body here..."
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "a34d1580a85b446fbf2ccdc2f5605a9f",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BranchNode",
    "m_ObjectId": "a397feef18ff44ffa83479d0c9d8f5f6",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Branch",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 147.00003051757813,
            "y": -60.0000114440918,
            "width": 171.99996948242188,
            "height": 141.99998474121095
        }
    },
    "m_Slots": [
        {
            "m_Id": "29359f5b01ae49cf93a2b3dba98baf8b"
        },
        {
            "m_Id": "088f301ae30e468f823e2aed0ec8b3e6"
        },
        {
            "m_Id": "6d3812b18ecf4e4884f8b4698c39dae8"
        },
        {
            "m_Id": "5cf6af3ff6eb4697b01b02973efa0e80"
        }
    ],
    "synonyms": [
        "switch",
        "if",
        "else"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "a95ad5b2974c431bbcb6ed8e552f444d",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ScreenPositionNode",
    "m_ObjectId": "aabd07734129467b94b3983667bfe3ad",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Screen Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -537.5000610351563,
            "y": -265.0,
            "width": 145.00003051757813,
            "height": 128.49998474121095
        }
    },
    "m_Slots": [
        {
            "m_Id": "16fdf1fc68264f31b684bbef2b9c4f37"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_ScreenSpaceType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ScreenPositionMaterialSlot",
    "m_ObjectId": "ac3e787696d9475692a17934439d165a",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": [],
    "m_ScreenSpaceType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "b03f8865c0d441c48a0cd9b9a2859041",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b2b7575a1ef0441f929c59c91d8cc13a",
    "m_Id": 0,
    "m_DisplayName": "Blurriness",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "b7b4220797164f5b974d4ca9a20ef508",
    "m_Id": 0,
    "m_DisplayName": "",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "bafad5a2077b4902b40c519abda57487",
    "m_Title": "",
    "m_Content": "Blurriness - higher is more blurry but also becomes more grainy unless Cycles and SamplesPerCycle are increased. Reduce Blurriness to reduce graininess and increase performance.\n\nCycles - the number of cycles to do in the spiral.\n\nSamplesPerCycle - the number of Scene Color samples per cylce\n\nTotal number of samples is Cycles x SamplerPerCycle. Lower total sample count will improve performance. Higher sample count will improve quality.\n\nBlur Multiplier - a multiplier for Blurriness.  Can be used to remove blur when set to zero or increase it when higher than one.\n\n",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -990.5,
        "y": 114.5,
        "width": 314.5,
        "height": 250.5
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "bc0ffa015c204b7d814d67af14f89115",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -651.5000610351563,
            "y": -127.50001525878906,
            "width": 129.0,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "446c286520cb4e5a8597117d5e831a49"
        },
        {
            "m_Id": "8c9bb6e32d5e49d5a5f5b8ab7bb2bcb1"
        },
        {
            "m_Id": "7727f5695e464c7689f373681b745505"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c23e111cd93f47c1a858f660f676bce0",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "c31a9639803d49c9b9c81dd4733fb535",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c704b982405e4087832086c1d6b915a0",
    "m_Id": 3,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c79fab84e0454c9b921c555de4974552",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "c7aecbe5f64d4fdc8adfb33996ed4a67",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -574.4999389648438,
            "y": 154.0,
            "width": 146.49996948242188,
            "height": 33.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "ec02391a1c944ed8a741250ec404176d"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "230b0011e7bc4fe58f0b4a6a30fb5a2c"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "caa04c7d85624e7ba41023bb5d598dc4",
    "m_Id": 6,
    "m_DisplayName": "Tex",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tex",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"e3d24661c1e055f45a7560c033dbb837\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "cb3b780941634c379027b79a032b6e08",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "ccf374770d304fe1826de502de59b69e",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 2.0,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "d2ee2d84e52740debe807f63eb40e5ec",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -652.0000610351563,
            "y": 526.5,
            "width": 126.00006103515625,
            "height": 118.00006103515625
        }
    },
    "m_Slots": [
        {
            "m_Id": "a95ad5b2974c431bbcb6ed8e552f444d"
        },
        {
            "m_Id": "ccf374770d304fe1826de502de59b69e"
        },
        {
            "m_Id": "237b9e969905404c8c874f534b854966"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "d67ab90ed7344299932b65b44821b186",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "de0e5fcecbeb4604a2d79e7bf60c1c8f",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 64.0,
        "y": 64.0,
        "z": 2.0,
        "w": 2.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "de399a0f648440a5b6a764b134f35987",
    "m_Id": 0,
    "m_DisplayName": "Blur Multiplier",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "de75195c6be64acbbbc7c8af0873e4eb",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "ff6c1a9c0487403e825804e84a165ced"
        },
        {
            "m_Id": "5112b74a42c74774877d9caed4877f96"
        },
        {
            "m_Id": "3d22fccbc87e4fc79cb191efcdc97052"
        },
        {
            "m_Id": "230b0011e7bc4fe58f0b4a6a30fb5a2c"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e1b0c4b0cdde40248b3f2af3ac7d276c",
    "m_Id": 2,
    "m_DisplayName": "Y",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Y",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "Y"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e48338b1e2614c2497cc7020ef18158d",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e66bc2fcbccb4b8da5fbcd5b85f041fe",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "e9375472d58e4f1fb4e95f5851142931",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e9de79cd2fa944f3a8f7f5e134abd88e",
    "m_Id": 2,
    "m_DisplayName": "Cycles",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cycles",
    "m_StageCapability": 3,
    "m_Value": 16.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ScreenNode",
    "m_ObjectId": "ea7e92bc22394e9c82eeb0a7f399639a",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Screen",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1047.0001220703125,
            "y": -127.50001525878906,
            "width": 87.50006103515625,
            "height": 101.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "88579388cd2f4121aa483d074153ac26"
        },
        {
            "m_Id": "3b87ec02601042679bbbb31a92e5e317"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ec02391a1c944ed8a741250ec404176d",
    "m_Id": 0,
    "m_DisplayName": "Blur Multiplier",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ef0acb5056e7424ebd36a8e78adabf58",
    "m_Id": 1,
    "m_DisplayName": "Height",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Height",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ScreenPositionNode",
    "m_ObjectId": "efd4ad4c478b46a985f4bedf9d8652f0",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Screen Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -353.5000305175781,
            "y": 445.0,
            "width": 144.99998474121095,
            "height": 128.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "e9375472d58e4f1fb4e95f5851142931"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_ScreenSpaceType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.RedirectNodeData",
    "m_ObjectId": "f38268c1ade6429c9abdc6329e312322",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Redirect Node",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -643.4999389648438,
            "y": 32.499969482421878,
            "width": 56.0,
            "height": 24.00006103515625
        }
    },
    "m_Slots": [
        {
            "m_Id": "b7b4220797164f5b974d4ca9a20ef508"
        },
        {
            "m_Id": "55d0f6d333314b3f8fb83f46f23aa5fb"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "f613324aa7ed45e79a8cf57d74171dcd",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BooleanMaterialSlot",
    "m_ObjectId": "fd7d7f7e93984b598100e1b2bba60ae1",
    "m_Id": 1,
    "m_DisplayName": "HDRP",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "HDRP",
    "m_StageCapability": 3,
    "m_Value": false,
    "m_DefaultValue": false
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "ff6c1a9c0487403e825804e84a165ced",
    "m_Guid": {
        "m_GuidSerialized": "d280b633-b5b4-4e3d-aae7-0fde9bf2e1e6"
    },
    "m_Name": "Blurriness",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Blurriness",
    "m_DefaultReferenceName": "_Blurriness",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 0.029999999329447748,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

