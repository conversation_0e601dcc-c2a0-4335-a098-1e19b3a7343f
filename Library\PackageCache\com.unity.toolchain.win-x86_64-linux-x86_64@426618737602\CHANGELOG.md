# Changelog
All notable changes to this package will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](http://semver.org/spec/v2.0.0.html).

## [2.0.10] - 2024-10-17

Optimized and reduced package size

## [2.0.9] - 2024-02-02

Updated to Clang 9.0.1

## [2.0.8] - 2024-01-12
Updated LICENSE.md

## [2.0.7] - 2023-12-21
bump version number to publish

## [2.0.6] - 2023-07-17

bump version number to publish

## [2.0.5] - 2023-05-03
bump version number to publish

## [2.0.4] - 2022-11-24
bump version number to publish

## [2.0.3] - 2022-10-31
add support for new IL2CPP arguments APIs for 2022.1.23f1+ editors

## [2.0.2] - 2022-05-23
bump version number to publish

## [2.0.1] - 2022-04-27
bump version number to publish

## [2.0.0] - 2022-03-28
- Fix 1336012 - Build from windows fails if username contains space
- Add support for new IL2CPP arguments APIs

## [1.0.0] - 2021-12-15
label as released package

## [0.1.21] - 2021-07-15

link libstdc++ statically

## [0.1.20] - 2021-04-30
fix issue on non-windows platforms

## [0.1.19] - 2021-02-22

use latest sysroot base

## [0.1.18] - 2021-01-11

use latest sysroot base

## [0.1.17] - 2020-11-24

use latest base and sysroot

## [0.1.16] - 2020-11-09

change namespace of NiceIO to prevent conflicts

## [0.1.15] - 2020-10-26

fixed issue where NiceIO would conflict with other packages using it

## [0.1.14] - 2020-09-29

Fixed issue where if you didn't have LinuxStandalone player installed would cause compiler errors

## [0.1.13] - 2020-09-23

use latest base

## [0.1.12] - 2020-09-22

use latest base

## [0.1.11] - 2020-08-08

use latest sysroot

## [0.1.10] - 2020-08-03

new toolchain payload

## [0.1.9] - 2020-07-26

use latest base and sysroot

## [0.1.8] - 2020-07-26

pickup validation meta file

## [0.1.7] - 2020-07-20

use latest base

## [0.1.6] - 2020-07-15

use latest sysroot & base

## [0.1.5] - 2020-05-27

documentation

## [0.1.4] - 2020-05-25

fix regression

## [0.1.3] - 2020-05-24

Reorganization

## [0.1.2] - 2020-05-21

Quote paths

## [0.1.1] - 2020-04-29

Pickup base changes

## [0.1.0] - 2020-04-25

### This is the first release of *Unity Package com.unity.toolchain.windows.x86_64*.

Intial release of windows x86_64 to linux x86_64 cross-compilation toolchain
