# 🚀 Unity AI Teammate Bot - Complete Setup Guide

## 📋 Quick Start Checklist

### ✅ Step 1: Unity Project Setup
1. **Create Unity Project**
   - Unity 2022.3 LTS or newer
   - 3D Template (URP or Built-in)
   - Name: "AI Teammate Bot"

2. **Import Scripts**
   - Copy all files from `Assets/Scripts/` to your project
   - Verify folder structure matches

### ✅ Step 2: Automated Scene Setup
1. **Create Scene Setup Object**
   ```
   - Create empty GameObject in scene
   - Add SceneSetup.cs component
   - Check "Auto Setup On Start" in inspector
   - Play scene OR right-click component → "Setup Scene"
   ```

2. **What Gets Created Automatically:**
   - ✅ Ground plane (100x100 units)
   - ✅ Player with camera and controls
   - ✅ AI Teammate with NavMesh agent
   - ✅ 3 Test enemies
   - ✅ Proper lighting
   - ✅ Color-coded objects (Blue=Player, Green=AI, Red=Enemies)

### ✅ Step 3: NavMesh Baking
1. **Open Navigation Window**
   ```
   Window > AI > Navigation
   ```

2. **Mark Objects as Static**
   ```
   - Select ground plane
   - Check "Navigation Static" in inspector
   ```

3. **Bake NavMesh**
   ```
   - Go to "Bake" tab in Navigation window
   - Click "Bake" button
   - Wait for blue NavMesh overlay to appear
   ```

### ✅ Step 4: Test the System
1. **Play the Scene**
   - Player: WASD movement, Mouse look
   - AI should follow you around

2. **Test Controls**
   ```
   T - Damage player
   Y - Down player (AI should revive)
   U - Send party invite to AI
   F1-F5 - Individual behavior tests
   F9 - Run all automated tests
   ```

---

## 🎯 Expected Behaviors

### 🤖 AI Teammate States
| State | Behavior | Trigger |
|-------|----------|---------|
| **Following** | Follows player at 3-unit distance | Player moves away |
| **Reviving** | Rushes to downed player, 3-second revive | Player health ≤ 0 |
| **Combat** | Attacks enemies within 15-unit range | Enemy detected |
| **Looting** | Collects yellow loot items | Loot spawned nearby |
| **Idle** | Stands still, occasional look around | No tasks needed |

### 🎮 Player Systems
- **Movement**: WASD + Mouse (FPS-style)
- **Health**: 100 HP, goes down at 0
- **Revive**: 30-second timer before permanent death
- **Events**: AI listens to player state changes

### 👾 Enemy Behavior
- **Detection**: 12-unit range for player/AI
- **Combat**: 8-unit attack range, 15 damage
- **Loot**: Drops yellow cube on death

---

## 🔧 Configuration Options

### TeammateAI Settings
```csharp
followDistance = 3f;        // Distance to maintain from player
reviveDistance = 2f;        // Distance needed to start reviving
reviveTime = 3f;           // Time to complete revive
attackRange = 15f;         // Combat engagement range
lootRange = 5f;           // Loot detection range
```

### ReviveSystem Settings
```csharp
maxDownedTime = 30f;       // Time before permanent death
downedEffect = null;       // Particle effect when downed
reviveEffect = null;       // Particle effect when revived
```

---

## 🧪 Testing & Validation

### Automated Tests (F9)
1. **Follow Test**: Moves player, checks if AI follows
2. **Revive Test**: Downs player, verifies AI revives
3. **Combat Test**: Spawns enemy, checks AI engagement
4. **Loot Test**: Spawns loot, verifies AI collection
5. **Party Test**: Sends invite, checks AI response

### Manual Testing
- Walk around → AI should follow
- Press Y → AI should rush to revive
- Let enemies approach → AI should fight
- Kill enemies → AI should collect loot

---

## 🚨 Troubleshooting

### Common Issues

**❌ AI Not Moving**
- ✅ Check NavMesh is baked (blue overlay visible)
- ✅ Verify NavMeshAgent component exists
- ✅ Ensure destination is on NavMesh

**❌ AI Not Following**
- ✅ Check player reference is assigned
- ✅ Verify followDistance settings
- ✅ Look for obstacles blocking path

**❌ Revive Not Working**
- ✅ Check ReviveSystem component exists
- ✅ Verify reviveDistance and reviveTime
- ✅ Ensure player is actually downed

**❌ Combat Issues**
- ✅ Verify enemy layer is set to 8
- ✅ Check attackRange and enemyLayer mask
- ✅ Ensure enemies have EnemyAI script

**❌ No UI Showing**
- ✅ GameManager creates UI automatically
- ✅ Check Canvas exists in scene
- ✅ Verify UI references in GameManager

### Debug Tools
- **Console Logs**: AI states and actions logged
- **Gizmos**: Enable to see detection ranges
- **Test Script**: Use AITeammateTest for validation
- **Scene View**: Check NavMesh overlay (blue areas)

---

## 🔮 Next Steps & Extensions

### Ready for Augment Code Enhancement
This system is designed for easy extension with Augment Code:

```csharp
// Example Augment-suggested improvements:
if (playerInDanger && !inCover)
    TakeCover();

if (multipleEnemies)
    UseAreaAttack();

if (playerLowHealth)
    PrioritizeHealing();
```

### Potential Enhancements
- **ML-Agents Integration**: Replace rule-based AI with learning
- **Voice Commands**: "Follow me", "Stay here", "Attack that"
- **Advanced Combat**: Cover system, flanking, team tactics
- **Inventory System**: AI manages shared resources
- **Multiplayer Ready**: Extend party system for real players

### Performance Optimizations
- **Object Pooling**: For enemies and loot
- **LOD System**: Reduce AI complexity at distance
- **Spatial Partitioning**: Optimize enemy/loot detection
- **Update Frequency**: Adjust AI update rates based on importance

---

## 📞 Support

If you encounter issues:
1. Check Console for error messages
2. Verify all components are properly assigned
3. Ensure NavMesh is baked correctly
4. Use the automated test system (F9)
5. Review this guide's troubleshooting section

**System Requirements:**
- Unity 2022.3 LTS+
- NavMesh components
- Standard Unity packages (no external dependencies)

**Tested Configurations:**
- ✅ Unity 2022.3 LTS + URP
- ✅ Unity 2022.3 LTS + Built-in RP
- ✅ Windows/Mac/Linux
- ✅ Various screen resolutions
