using UnityEngine;
using System.Collections;

/// <summary>
/// Test script to validate AI Teammate functionality
/// Attach to any GameObject in the scene to run automated tests
/// </summary>
public class AITeammateTest : MonoBehaviour
{
    [Header("Test Settings")]
    public bool runTestsOnStart = false;
    public float testDelay = 2f;
    
    [Header("References")]
    public PlayerController player;
    public TeammateAI teammate;
    public EnemyAI[] enemies;
    
    private bool testsRunning = false;
    
    void Start()
    {
        // Auto-find components if not assigned
        if (player == null)
            player = FindObjectOfType<PlayerController>();
        
        if (teammate == null)
            teammate = FindObjectOfType<TeammateAI>();
        
        if (enemies == null || enemies.Length == 0)
            enemies = FindObjectsOfType<EnemyAI>();
        
        if (runTestsOnStart)
        {
            StartCoroutine(RunAllTests());
        }
    }
    
    void Update()
    {
        // Manual test triggers
        if (Input.GetKeyDown(KeyCode.F1))
        {
            TestFollowBehavior();
        }
        
        if (Input.GetKeyDown(KeyCode.F2))
        {
            TestReviveBehavior();
        }
        
        if (Input.GetKeyDown(KeyCode.F3))
        {
            TestCombatBehavior();
        }
        
        if (Input.GetKeyDown(KeyCode.F4))
        {
            TestLootBehavior();
        }
        
        if (Input.GetKeyDown(KeyCode.F5))
        {
            TestPartyInvite();
        }
        
        if (Input.GetKeyDown(KeyCode.F9))
        {
            if (!testsRunning)
                StartCoroutine(RunAllTests());
        }
    }
    
    [ContextMenu("Run All Tests")]
    public void RunAllTestsMenu()
    {
        if (!testsRunning)
            StartCoroutine(RunAllTests());
    }
    
    IEnumerator RunAllTests()
    {
        testsRunning = true;
        Debug.Log("=== Starting AI Teammate Tests ===");
        
        yield return StartCoroutine(TestFollowBehaviorCoroutine());
        yield return new WaitForSeconds(testDelay);
        
        yield return StartCoroutine(TestReviveBehaviorCoroutine());
        yield return new WaitForSeconds(testDelay);
        
        yield return StartCoroutine(TestCombatBehaviorCoroutine());
        yield return new WaitForSeconds(testDelay);
        
        yield return StartCoroutine(TestLootBehaviorCoroutine());
        yield return new WaitForSeconds(testDelay);
        
        TestPartyInvite();
        
        Debug.Log("=== All AI Teammate Tests Complete ===");
        testsRunning = false;
    }
    
    [ContextMenu("Test Follow Behavior")]
    public void TestFollowBehavior()
    {
        StartCoroutine(TestFollowBehaviorCoroutine());
    }
    
    IEnumerator TestFollowBehaviorCoroutine()
    {
        Debug.Log("TEST: Follow Behavior");
        
        if (player == null || teammate == null)
        {
            Debug.LogError("Player or Teammate not found!");
            yield break;
        }
        
        Vector3 originalPlayerPos = player.transform.position;
        Vector3 originalTeammatePos = teammate.transform.position;
        
        // Move player away
        Vector3 newPlayerPos = originalPlayerPos + Vector3.forward * 10f;
        player.transform.position = newPlayerPos;
        
        Debug.Log($"Moved player to {newPlayerPos}. Teammate should follow.");
        
        // Wait and check if teammate follows
        yield return new WaitForSeconds(3f);
        
        float distance = Vector3.Distance(player.transform.position, teammate.transform.position);
        if (distance <= teammate.followDistance + 2f)
        {
            Debug.Log("✅ PASS: Teammate successfully followed player");
        }
        else
        {
            Debug.Log($"❌ FAIL: Teammate did not follow. Distance: {distance}");
        }
        
        // Reset positions
        player.transform.position = originalPlayerPos;
    }
    
    [ContextMenu("Test Revive Behavior")]
    public void TestReviveBehavior()
    {
        StartCoroutine(TestReviveBehaviorCoroutine());
    }
    
    IEnumerator TestReviveBehaviorCoroutine()
    {
        Debug.Log("TEST: Revive Behavior");
        
        if (player == null || teammate == null)
        {
            Debug.LogError("Player or Teammate not found!");
            yield break;
        }
        
        // Down the player
        player.GoDown();
        Debug.Log("Player downed. Teammate should come to revive.");
        
        // Wait for teammate to reach and revive
        float timeout = 10f;
        float timer = 0f;
        
        while (player.IsPlayerDowned() && timer < timeout)
        {
            timer += Time.deltaTime;
            yield return null;
        }
        
        if (!player.IsPlayerDowned())
        {
            Debug.Log("✅ PASS: Teammate successfully revived player");
        }
        else
        {
            Debug.Log("❌ FAIL: Teammate did not revive player in time");
            // Force revive to continue tests
            player.Revive();
        }
    }
    
    [ContextMenu("Test Combat Behavior")]
    public void TestCombatBehavior()
    {
        StartCoroutine(TestCombatBehaviorCoroutine());
    }
    
    IEnumerator TestCombatBehaviorCoroutine()
    {
        Debug.Log("TEST: Combat Behavior");
        
        if (teammate == null)
        {
            Debug.LogError("Teammate not found!");
            yield break;
        }
        
        // Spawn a test enemy near the teammate
        GameObject testEnemy = GameObject.CreatePrimitive(PrimitiveType.Cube);
        testEnemy.name = "TestEnemy";
        testEnemy.layer = 8; // Enemy layer
        testEnemy.transform.position = teammate.transform.position + Vector3.forward * 5f;
        
        EnemyAI enemyAI = testEnemy.AddComponent<EnemyAI>();
        testEnemy.AddComponent<UnityEngine.AI.NavMeshAgent>();
        
        Debug.Log("Spawned test enemy. Teammate should engage in combat.");
        
        // Wait and observe combat behavior
        yield return new WaitForSeconds(5f);
        
        // Check if teammate is in combat state
        if (teammate.currentState == TeammateAI.AIState.Combat)
        {
            Debug.Log("✅ PASS: Teammate engaged in combat");
        }
        else
        {
            Debug.Log($"❌ FAIL: Teammate not in combat state. Current state: {teammate.currentState}");
        }
        
        // Clean up test enemy
        if (testEnemy != null)
        {
            Destroy(testEnemy);
        }
    }
    
    [ContextMenu("Test Loot Behavior")]
    public void TestLootBehavior()
    {
        StartCoroutine(TestLootBehaviorCoroutine());
    }
    
    IEnumerator TestLootBehaviorCoroutine()
    {
        Debug.Log("TEST: Loot Behavior");
        
        if (teammate == null)
        {
            Debug.LogError("Teammate not found!");
            yield break;
        }
        
        // Create test loot near teammate
        GameObject testLoot = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        testLoot.name = "TestLoot";
        testLoot.layer = 9; // Loot layer
        testLoot.transform.position = teammate.transform.position + Vector3.right * 3f;
        testLoot.transform.localScale = Vector3.one * 0.3f;
        
        Renderer lootRenderer = testLoot.GetComponent<Renderer>();
        if (lootRenderer != null)
        {
            lootRenderer.material.color = Color.yellow;
        }
        
        Debug.Log("Spawned test loot. Teammate should collect it.");
        
        // Wait and check if loot is collected
        yield return new WaitForSeconds(5f);
        
        if (testLoot == null)
        {
            Debug.Log("✅ PASS: Teammate successfully collected loot");
        }
        else
        {
            Debug.Log("❌ FAIL: Teammate did not collect loot");
            // Clean up
            Destroy(testLoot);
        }
    }
    
    [ContextMenu("Test Party Invite")]
    public void TestPartyInvite()
    {
        Debug.Log("TEST: Party Invite");
        
        if (teammate == null)
        {
            Debug.LogError("Teammate not found!");
            return;
        }
        
        teammate.ReceivePartyInvite("TestPlayer123");
        Debug.Log("✅ PASS: Party invite sent and received");
    }
    
    void OnGUI()
    {
        if (testsRunning)
        {
            GUI.Label(new Rect(10, 10, 200, 20), "Running AI Tests...");
        }
        
        // Test controls
        GUI.Label(new Rect(10, Screen.height - 150, 300, 20), "AI Teammate Test Controls:");
        GUI.Label(new Rect(10, Screen.height - 130, 300, 20), "F1 - Test Follow Behavior");
        GUI.Label(new Rect(10, Screen.height - 110, 300, 20), "F2 - Test Revive Behavior");
        GUI.Label(new Rect(10, Screen.height - 90, 300, 20), "F3 - Test Combat Behavior");
        GUI.Label(new Rect(10, Screen.height - 70, 300, 20), "F4 - Test Loot Behavior");
        GUI.Label(new Rect(10, Screen.height - 50, 300, 20), "F5 - Test Party Invite");
        GUI.Label(new Rect(10, Screen.height - 30, 300, 20), "F9 - Run All Tests");
        
        // Status display
        if (teammate != null)
        {
            GUI.Label(new Rect(Screen.width - 200, 10, 190, 20), $"AI State: {teammate.currentState}");
        }
        
        if (player != null)
        {
            string playerStatus = player.IsPlayerDowned() ? "DOWNED" : "ALIVE";
            GUI.Label(new Rect(Screen.width - 200, 30, 190, 20), $"Player: {playerStatus}");
        }
    }
}
