using UnityEngine;
using UnityEngine.AI;

public class EnemyAI : MonoBeh<PERSON>our
{
    [Header("Enemy Settings")]
    public float maxHealth = 50f;
    public float currentHealth;
    public float attackRange = 8f;
    public float damage = 15f;
    public float attackCooldown = 2f;
    public float moveSpeed = 3f;
    
    [Header("Detection")]
    public float detectionRange = 12f;
    public LayerMask playerLayer = 1 << 0; // Player on default layer
    
    private NavMeshAgent agent;
    private Transform target;
    private float lastAttackTime;
    private EnemyState currentState = EnemyState.Idle;
    
    public enum EnemyState
    {
        Idle,
        Chasing,
        Attacking,
        Dead
    }
    
    void Start()
    {
        agent = GetComponent<NavMeshAgent>();
        if (agent == null)
        {
            agent = gameObject.AddComponent<NavMeshAgent>();
        }

        currentHealth = maxHealth;
        agent.speed = moveSpeed;

        // Set enemy layer
        gameObject.layer = 8; // Enemy layer

        // Wait a frame for NavMesh to initialize, then start updates
        StartCoroutine(InitializeAfterFrame());
    }

    System.Collections.IEnumerator InitializeAfterFrame()
    {
        yield return new WaitForEndOfFrame();

        // Ensure agent is on NavMesh before starting updates
        if (agent.isOnNavMesh)
        {
            InvokeRepeating(nameof(UpdateEnemy), 0f, 0.2f); // Update 5 times per second
        }
        else
        {
            Debug.LogWarning($"Enemy {gameObject.name} is not on NavMesh. Attempting to place...");
            TryPlaceOnNavMesh();
        }
    }

    void TryPlaceOnNavMesh()
    {
        UnityEngine.AI.NavMeshHit hit;
        if (UnityEngine.AI.NavMesh.SamplePosition(transform.position, out hit, 10f, UnityEngine.AI.NavMesh.AllAreas))
        {
            transform.position = hit.position;
            if (agent.isOnNavMesh)
            {
                InvokeRepeating(nameof(UpdateEnemy), 0f, 0.2f);
                Debug.Log($"Successfully placed {gameObject.name} on NavMesh");
            }
        }
        else
        {
            Debug.LogError($"Could not place {gameObject.name} on NavMesh. Disabling NavMeshAgent.");
            agent.enabled = false;
        }
    }
    
    void UpdateEnemy()
    {
        if (currentState == EnemyState.Dead) return;
        
        // Find targets
        FindTarget();
        
        // Update state based on target
        UpdateState();
        
        // Execute behavior
        switch (currentState)
        {
            case EnemyState.Idle:
                IdleBehavior();
                break;
            case EnemyState.Chasing:
                ChaseBehavior();
                break;
            case EnemyState.Attacking:
                AttackBehavior();
                break;
        }
    }
    
    void FindTarget()
    {
        // Look for player first
        Collider[] players = Physics.OverlapSphere(transform.position, detectionRange, playerLayer);
        
        if (players.Length > 0)
        {
            target = players[0].transform;
            return;
        }
        
        // Look for AI teammate
        TeammateAI teammate = FindObjectOfType<TeammateAI>();
        if (teammate != null)
        {
            float distanceToTeammate = Vector3.Distance(transform.position, teammate.transform.position);
            if (distanceToTeammate <= detectionRange)
            {
                target = teammate.transform;
                return;
            }
        }
        
        target = null;
    }
    
    void UpdateState()
    {
        if (target == null)
        {
            currentState = EnemyState.Idle;
            return;
        }
        
        float distanceToTarget = Vector3.Distance(transform.position, target.position);
        
        if (distanceToTarget <= attackRange)
        {
            currentState = EnemyState.Attacking;
        }
        else if (distanceToTarget <= detectionRange)
        {
            currentState = EnemyState.Chasing;
        }
        else
        {
            currentState = EnemyState.Idle;
        }
    }
    
    void IdleBehavior()
    {
        if (!IsAgentValid()) return;

        agent.ResetPath();

        // Patrol or look around
        if (Random.Range(0f, 1f) < 0.02f) // 2% chance per update
        {
            Vector3 randomDirection = Random.insideUnitSphere * 5f;
            randomDirection += transform.position;
            randomDirection.y = transform.position.y;

            NavMeshHit hit;
            if (NavMesh.SamplePosition(randomDirection, out hit, 5f, NavMesh.AllAreas))
            {
                agent.SetDestination(hit.position);
            }
        }
    }
    
    void ChaseBehavior()
    {
        if (target == null || !IsAgentValid()) return;

        agent.SetDestination(target.position);

        // Look at target
        Vector3 lookDirection = (target.position - transform.position).normalized;
        if (lookDirection != Vector3.zero)
        {
            transform.rotation = Quaternion.LookRotation(lookDirection);
        }
    }
    
    void AttackBehavior()
    {
        if (target == null || !IsAgentValid()) return;

        // Stop moving
        agent.ResetPath();

        // Look at target
        Vector3 lookDirection = (target.position - transform.position).normalized;
        if (lookDirection != Vector3.zero)
        {
            transform.rotation = Quaternion.LookRotation(lookDirection);
        }

        // Attack if cooldown is ready
        if (Time.time >= lastAttackTime + attackCooldown)
        {
            Attack();
            lastAttackTime = Time.time;
        }
    }
    
    void Attack()
    {
        if (target == null) return;
        
        // Simple raycast attack
        RaycastHit hit;
        Vector3 direction = (target.position - transform.position).normalized;
        
        if (Physics.Raycast(transform.position + Vector3.up, direction, out hit, attackRange))
        {
            // Check if we hit the player
            PlayerController player = hit.collider.GetComponent<PlayerController>();
            if (player != null)
            {
                player.TakeDamage(damage);
                Debug.Log($"Enemy attacked player for {damage} damage!");
                return;
            }
            
            // Check if we hit the AI teammate
            TeammateAI teammate = hit.collider.GetComponent<TeammateAI>();
            if (teammate != null)
            {
                // AI teammate takes damage (you can implement health for AI too)
                Debug.Log($"Enemy attacked AI teammate for {damage} damage!");
                return;
            }
        }
    }
    
    public void TakeDamage(float damageAmount)
    {
        if (currentState == EnemyState.Dead) return;

        // Use EnemyHealth component if available
        EnemyHealth healthComponent = GetComponent<EnemyHealth>();
        if (healthComponent != null)
        {
            healthComponent.TakeDamage(damageAmount);
            return;
        }

        // Fallback to old system
        currentHealth -= damageAmount;
        currentHealth = Mathf.Clamp(currentHealth, 0f, maxHealth);

        Debug.Log($"Enemy took {damageAmount} damage. Health: {currentHealth}/{maxHealth}");

        if (currentHealth <= 0f)
        {
            Die();
        }
    }

    // Called by EnemyHealth component when health reaches 0
    public void OnHealthDepleted()
    {
        Die();
    }
    
    void Die()
    {
        currentState = EnemyState.Dead;
        agent.enabled = false;
        
        Debug.Log("Enemy died!");
        
        // Spawn loot
        SpawnLoot();
        
        // Destroy after a delay
        Destroy(gameObject, 2f);
    }
    
    void SpawnLoot()
    {
        // Create a simple loot object
        GameObject loot = GameObject.CreatePrimitive(PrimitiveType.Cube);
        loot.transform.position = transform.position + Vector3.up * 0.5f;
        loot.transform.localScale = Vector3.one * 0.3f;
        loot.layer = 9; // Loot layer
        
        // Make it yellow to indicate loot
        Renderer renderer = loot.GetComponent<Renderer>();
        if (renderer != null)
        {
            renderer.material.color = Color.yellow;
        }
        
        // Add a tag for identification
        loot.tag = "Loot";
        loot.name = "Loot_" + Random.Range(1000, 9999);
        
        Debug.Log($"Enemy dropped loot: {loot.name}");
    }
    
    // Helper method to validate NavMeshAgent state
    bool IsAgentValid()
    {
        return agent != null && agent.enabled && agent.isOnNavMesh && agent.isActiveAndEnabled;
    }

    void OnDrawGizmosSelected()
    {
        // Draw detection range
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, detectionRange);

        // Draw attack range
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, attackRange);
    }
}
