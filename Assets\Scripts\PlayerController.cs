using UnityEngine;

public class PlayerController : MonoBehaviour
{
    [Header("Movement Settings")]
    public float moveSpeed = 5f;
    public float mouseSensitivity = 2f;

    [Header("Health Settings")]
    public float maxHealth = 100f;
    public float currentHealth;

    [Header("Components")]
    public Camera playerCamera;
    public Transform cameraTransform;

    private CharacterController characterController;
    private float verticalRotation = 0f;
    private Vector3 velocity;
    private bool isGrounded;
    private ReviveSystem reviveSystem;

    // Events for AI teammate to listen to
    public System.Action<bool> OnPlayerDownedChanged;
    public System.Action<Vector3> OnPlayerMoved;
    
    void Start()
    {
        characterController = GetComponent<CharacterController>();
        if (characterController == null)
        {
            characterController = gameObject.AddComponent<CharacterController>();
        }

        // Get or add ReviveSystem component
        reviveSystem = GetComponent<ReviveSystem>();
        if (reviveSystem == null)
        {
            reviveSystem = gameObject.AddComponent<ReviveSystem>();
        }

        currentHealth = maxHealth;

        // Lock cursor to center of screen
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;

        if (playerCamera == null)
        {
            playerCamera = Camera.main;
        }

        if (cameraTransform == null && playerCamera != null)
        {
            cameraTransform = playerCamera.transform;
        }
    }
    
    void Update()
    {
        if (!reviveSystem.isDown)
        {
            HandleMovement();
            HandleMouseLook();
        }

        // Check for ground
        isGrounded = characterController.isGrounded;

        // Apply gravity
        if (isGrounded && velocity.y < 0)
        {
            velocity.y = -2f;
        }
        velocity.y += Physics.gravity.y * Time.deltaTime;

        characterController.Move(velocity * Time.deltaTime);
    }
    
    void HandleMovement()
    {
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");
        
        Vector3 direction = transform.right * horizontal + transform.forward * vertical;
        Vector3 move = direction * moveSpeed;
        
        characterController.Move(move * Time.deltaTime);
        
        // Notify AI teammate of movement
        if (move.magnitude > 0.1f)
        {
            OnPlayerMoved?.Invoke(transform.position);
        }
    }
    
    void HandleMouseLook()
    {
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;
        
        // Rotate the player body horizontally
        transform.Rotate(Vector3.up * mouseX);
        
        // Rotate the camera vertically
        verticalRotation -= mouseY;
        verticalRotation = Mathf.Clamp(verticalRotation, -90f, 90f);
        
        if (cameraTransform != null)
        {
            cameraTransform.localRotation = Quaternion.Euler(verticalRotation, 0f, 0f);
        }
    }
    
    public void TakeDamage(float damage)
    {
        if (reviveSystem.isDown) return;

        currentHealth -= damage;
        currentHealth = Mathf.Clamp(currentHealth, 0f, maxHealth);

        if (currentHealth <= 0f && !reviveSystem.isDown)
        {
            reviveSystem.Down();
            OnPlayerDownedChanged?.Invoke(true);
        }
    }

    public void GoDown()
    {
        reviveSystem.Down();
        OnPlayerDownedChanged?.Invoke(true);
        Debug.Log("Player is downed! Teammate should come revive.");
    }

    public void Revive(float healthAmount = 30f)
    {
        if (!reviveSystem.isDown) return;

        reviveSystem.Revive();
        currentHealth = healthAmount;
        OnPlayerDownedChanged?.Invoke(false);

        Debug.Log("Player has been revived!");
    }
    
    public void Heal(float healAmount)
    {
        if (reviveSystem.isDown) return;

        currentHealth += healAmount;
        currentHealth = Mathf.Clamp(currentHealth, 0f, maxHealth);
    }

    // Utility methods for AI teammate
    public Vector3 GetPosition() => transform.position;
    public bool IsPlayerDowned() => reviveSystem.isDown;
    public float GetHealthPercentage() => currentHealth / maxHealth;
    public ReviveSystem GetReviveSystem() => reviveSystem;
}
