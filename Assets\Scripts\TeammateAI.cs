using UnityEngine;
using UnityEngine.AI;
using System.Collections.Generic;

public class TeammateAI : MonoBehaviour
{
    [Header("AI Settings")]
    public float followDistance = 3f;
    public float maxFollowDistance = 10f;
    public float reviveDistance = 2f;
    public float reviveTime = 3f;
    public float attackRange = 15f;
    public float lootRange = 5f;
    
    [Header("Combat Settings")]
    public float damage = 25f;
    public float fireRate = 2f;
    public LayerMask enemyLayer = 1 << 8; // Enemies on layer 8
    public LayerMask lootLayer = 1 << 9; // Loot on layer 9
    
    [Header("References")]
    public PlayerController player;
    public Transform firePoint;
    public GameObject bulletPrefab;
    
    private NavMeshAgent agent;
    private AIState currentState = AIState.Following;
    private float lastFireTime;
    private float reviveProgress;
    private Transform currentTarget;
    private List<Transform> nearbyLoot = new List<Transform>();
    
    public enum AIState
    {
        Following,
        Reviving,
        Combat,
        Looting,
        Idle
    }
    
    void Start()
    {
        agent = GetComponent<NavMeshAgent>();
        if (agent == null)
        {
            agent = gameObject.AddComponent<NavMeshAgent>();
        }
        
        // Find player if not assigned
        if (player == null)
        {
            player = FindObjectOfType<PlayerController>();
        }
        
        // Subscribe to player events
        if (player != null)
        {
            player.OnPlayerDownedChanged += OnPlayerDownedChanged;
            player.OnPlayerMoved += OnPlayerMoved;
        }
        
        // Set up fire point if not assigned
        if (firePoint == null)
        {
            GameObject firePointObj = new GameObject("FirePoint");
            firePointObj.transform.SetParent(transform);
            firePointObj.transform.localPosition = Vector3.forward + Vector3.up;
            firePoint = firePointObj.transform;
        }
        
        InvokeRepeating(nameof(UpdateAI), 0f, 0.1f); // Update AI 10 times per second
    }
    
    void UpdateAI()
    {
        if (player == null) return;
        
        // Check for state transitions
        CheckStateTransitions();
        
        // Execute current state behavior
        switch (currentState)
        {
            case AIState.Following:
                FollowPlayer();
                break;
            case AIState.Reviving:
                RevivePlayer();
                break;
            case AIState.Combat:
                CombatBehavior();
                break;
            case AIState.Looting:
                LootBehavior();
                break;
            case AIState.Idle:
                IdleBehavior();
                break;
        }
    }
    
    void CheckStateTransitions()
    {
        float distanceToPlayer = Vector3.Distance(transform.position, player.transform.position);
        ReviveSystem reviveSystem = player.GetReviveSystem();

        // Priority 1: Revive downed player
        if (reviveSystem.NeedsRevive() && distanceToPlayer <= reviveDistance)
        {
            ChangeState(AIState.Reviving);
            return;
        }

        // Priority 2: Combat with nearby enemies
        Transform nearestEnemy = FindNearestEnemy();
        if (nearestEnemy != null)
        {
            currentTarget = nearestEnemy;
            ChangeState(AIState.Combat);
            return;
        }

        // Priority 3: Follow downed player to revive
        if (reviveSystem.NeedsRevive())
        {
            ChangeState(AIState.Following);
            return;
        }

        // Priority 4: Loot nearby items
        if (FindNearbyLoot().Count > 0 && distanceToPlayer <= maxFollowDistance)
        {
            ChangeState(AIState.Looting);
            return;
        }

        // Priority 5: Follow player if too far
        if (distanceToPlayer > followDistance)
        {
            ChangeState(AIState.Following);
            return;
        }

        // Default: Idle
        ChangeState(AIState.Idle);
    }
    
    void ChangeState(AIState newState)
    {
        if (currentState == newState) return;
        
        // Exit current state
        switch (currentState)
        {
            case AIState.Reviving:
                reviveProgress = 0f;
                break;
        }
        
        currentState = newState;
        Debug.Log($"AI State changed to: {newState}");
    }
    
    void FollowPlayer()
    {
        if (player == null) return;
        
        Vector3 targetPosition = player.transform.position;
        float distance = Vector3.Distance(transform.position, targetPosition);
        
        if (distance > followDistance)
        {
            agent.SetDestination(targetPosition);
        }
        else
        {
            agent.ResetPath();
        }
    }
    
    void RevivePlayer()
    {
        ReviveSystem reviveSystem = player.GetReviveSystem();
        if (!reviveSystem.NeedsRevive()) return;

        // Move to player if not close enough
        float distance = Vector3.Distance(transform.position, player.transform.position);
        if (distance > reviveDistance)
        {
            agent.SetDestination(player.transform.position);
            return;
        }

        // Stop moving and start reviving
        agent.ResetPath();

        // Look at player
        Vector3 lookDirection = (player.transform.position - transform.position).normalized;
        if (lookDirection != Vector3.zero)
        {
            transform.rotation = Quaternion.LookRotation(lookDirection);
        }

        // Revive progress
        reviveProgress += Time.deltaTime;

        if (reviveProgress >= reviveTime)
        {
            player.Revive();
            reviveProgress = 0f;
        }
    }
    
    void CombatBehavior()
    {
        if (currentTarget == null)
        {
            currentTarget = FindNearestEnemy();
            if (currentTarget == null) return;
        }
        
        float distanceToTarget = Vector3.Distance(transform.position, currentTarget.position);
        
        // Move closer if too far
        if (distanceToTarget > attackRange)
        {
            agent.SetDestination(currentTarget.position);
        }
        else
        {
            // Stop and shoot
            agent.ResetPath();
            
            // Look at target
            Vector3 lookDirection = (currentTarget.position - transform.position).normalized;
            if (lookDirection != Vector3.zero)
            {
                transform.rotation = Quaternion.LookRotation(lookDirection);
            }
            
            // Shoot
            if (Time.time >= lastFireTime + (1f / fireRate))
            {
                Shoot(currentTarget);
                lastFireTime = Time.time;
            }
        }
    }
    
    void LootBehavior()
    {
        List<Transform> loot = FindNearbyLoot();
        if (loot.Count == 0) return;
        
        Transform nearestLoot = loot[0];
        float nearestDistance = Vector3.Distance(transform.position, nearestLoot.position);
        
        foreach (Transform lootItem in loot)
        {
            float distance = Vector3.Distance(transform.position, lootItem.position);
            if (distance < nearestDistance)
            {
                nearestLoot = lootItem;
                nearestDistance = distance;
            }
        }
        
        if (nearestDistance <= 1f)
        {
            // Collect loot
            CollectLoot(nearestLoot);
        }
        else
        {
            agent.SetDestination(nearestLoot.position);
        }
    }
    
    void IdleBehavior()
    {
        agent.ResetPath();
        
        // Look around occasionally
        if (Random.Range(0f, 1f) < 0.01f) // 1% chance per frame
        {
            float randomAngle = Random.Range(0f, 360f);
            transform.rotation = Quaternion.Euler(0f, randomAngle, 0f);
        }
    }
    
    Transform FindNearestEnemy()
    {
        Collider[] enemies = Physics.OverlapSphere(transform.position, attackRange, enemyLayer);
        Transform nearest = null;
        float nearestDistance = float.MaxValue;
        
        foreach (Collider enemy in enemies)
        {
            float distance = Vector3.Distance(transform.position, enemy.transform.position);
            if (distance < nearestDistance)
            {
                nearest = enemy.transform;
                nearestDistance = distance;
            }
        }
        
        return nearest;
    }
    
    List<Transform> FindNearbyLoot()
    {
        nearbyLoot.Clear();
        Collider[] lootItems = Physics.OverlapSphere(transform.position, lootRange, lootLayer);
        
        foreach (Collider loot in lootItems)
        {
            nearbyLoot.Add(loot.transform);
        }
        
        return nearbyLoot;
    }
    
    void Shoot(Transform target)
    {
        if (bulletPrefab != null && firePoint != null)
        {
            GameObject bullet = Instantiate(bulletPrefab, firePoint.position, firePoint.rotation);
            // Add bullet logic here (Rigidbody, damage, etc.)
        }
        
        // Simple raycast damage for now
        RaycastHit hit;
        Vector3 direction = (target.position - firePoint.position).normalized;
        
        if (Physics.Raycast(firePoint.position, direction, out hit, attackRange))
        {
            EnemyAI enemy = hit.collider.GetComponent<EnemyAI>();
            if (enemy != null)
            {
                enemy.TakeDamage(damage);
                Debug.Log($"AI hit enemy for {damage} damage!");
            }
        }
    }
    
    void CollectLoot(Transform lootItem)
    {
        Debug.Log($"AI collected loot: {lootItem.name}");
        Destroy(lootItem.gameObject);
    }
    
    void OnPlayerDownedChanged(bool isDowned)
    {
        if (isDowned)
        {
            Debug.Log("Player is down! AI rushing to revive.");
        }
    }
    
    void OnPlayerMoved(Vector3 newPosition)
    {
        // Player moved, AI might need to follow
    }
    
    void OnDestroy()
    {
        if (player != null)
        {
            player.OnPlayerDownedChanged -= OnPlayerDownedChanged;
            player.OnPlayerMoved -= OnPlayerMoved;
        }
    }
    
    // Public method for party invites (mock implementation)
    public void ReceivePartyInvite(string playerName)
    {
        Debug.Log($"AI received party invite from {playerName}. Accepting!");
        // In a real game, this would handle multiplayer party logic
    }
}
