%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ec995e51a6e251468d2a3fd8a686257, type: 3}
  m_Name: UniversalRenderPipelineGlobalSettings
  m_EditorClassIdentifier: 
  m_ShaderStrippingSetting:
    m_Version: 0
    m_ExportShaderVariants: 1
    m_ShaderVariantLogLevel: 0
    m_StripRuntimeDebugShaders: 1
  m_URPShaderStrippingSetting:
    m_Version: 0
    m_StripUnusedPostProcessingVariants: 0
    m_StripUnusedVariants: 1
    m_StripScreenCoordOverrideVariants: 1
  m_ShaderVariantLogLevel: 0
  m_ExportShaderVariants: 1
  m_StripDebugVariants: 1
  m_StripUnusedPostProcessingVariants: 0
  m_StripUnusedVariants: 1
  m_StripScreenCoordOverrideVariants: 1
  supportRuntimeDebugDisplay: 0
  m_EnableRenderGraph: 0
  m_Settings:
    m_SettingsList:
      m_List:
      - rid: 8716090808467646
      - rid: 8716090808467647
      - rid: 8716090808467648
      - rid: 8716090808467649
      - rid: 8716090808467650
      - rid: 8716090808467651
      - rid: 8716090808467652
      - rid: 8716090808467653
      - rid: 8716090808467654
      - rid: 8716090808467655
      - rid: 8716090808467656
      - rid: 8716090808467657
      - rid: 8716090808467658
      - rid: 8716090808467659
      - rid: 8716090808467660
      - rid: 8716090808467661
      - rid: 8716090808467662
      - rid: 8716090808467663
      - rid: 8716090808467664
      - rid: 8716090808467665
      - rid: 8716090808467666
      - rid: 8716090808467667
      - rid: 8716090808467668
      - rid: 8716090808467669
      - rid: 8716090808467670
      - rid: 8716090808467671
      - rid: 8716090808467672
      - rid: 8716090808467673
    m_RuntimeSettings:
      m_List: []
  m_AssetVersion: 8
  m_ObsoleteDefaultVolumeProfile: {fileID: 0}
  m_RenderingLayerNames:
  - Default
  m_ValidRenderingLayers: 1
  lightLayerName0: 
  lightLayerName1: 
  lightLayerName2: 
  lightLayerName3: 
  lightLayerName4: 
  lightLayerName5: 
  lightLayerName6: 
  lightLayerName7: 
  apvScenesData:
    obsoleteSceneBounds:
      m_Keys: []
      m_Values: []
    obsoleteHasProbeVolumes:
      m_Keys: []
      m_Values: 
  references:
    version: 2
    RefIds:
    - rid: 8716090808467646
      type: {class: Renderer2DResources, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        m_Version: 0
        m_LightShader: {fileID: 4800000, guid: 3f6c848ca3d7bca4bbe846546ac701a1, type: 3}
        m_ProjectedShadowShader: {fileID: 4800000, guid: ce09d4a80b88c5a4eb9768fab4f1ee00, type: 3}
        m_SpriteShadowShader: {fileID: 4800000, guid: 44fc62292b65ab04eabcf310e799ccf6, type: 3}
        m_SpriteUnshadowShader: {fileID: 4800000, guid: de02b375720b5c445afe83cd483bedf3, type: 3}
        m_GeometryShadowShader: {fileID: 4800000, guid: 19349a0f9a7ed4c48a27445bcf92e5e1, type: 3}
        m_GeometryUnshadowShader: {fileID: 4800000, guid: 77774d9009bb81447b048c907d4c6273, type: 3}
        m_FallOffLookup: {fileID: 2800000, guid: 5688ab254e4c0634f8d6c8e0792331ca, type: 3}
        m_CopyDepthPS: {fileID: 4800000, guid: d6dae50ee9e1bfa4db75f19f99355220, type: 3}
        m_DefaultLitMaterial: {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
        m_DefaultUnlitMaterial: {fileID: 2100000, guid: 9dfc825aed78fcd4ba02077103263b40, type: 2}
        m_DefaultMaskMaterial: {fileID: 2100000, guid: 15d0c3709176029428a0da2f8cecf0b5, type: 2}
    - rid: 8716090808467647
      type: {class: PostProcessData/ShaderResources, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        stopNanPS: {fileID: 4800000, guid: 1121bb4e615ca3c48b214e79e841e823, type: 3}
        subpixelMorphologicalAntialiasingPS: {fileID: 4800000, guid: 63eaba0ebfb82cc43bde059b4a8c65f6, type: 3}
        gaussianDepthOfFieldPS: {fileID: 4800000, guid: 5e7134d6e63e0bc47a1dd2669cedb379, type: 3}
        bokehDepthOfFieldPS: {fileID: 4800000, guid: 2aed67ad60045d54ba3a00c91e2d2631, type: 3}
        cameraMotionBlurPS: {fileID: 4800000, guid: 1edcd131364091c46a17cbff0b1de97a, type: 3}
        paniniProjectionPS: {fileID: 4800000, guid: a15b78cf8ca26ca4fb2090293153c62c, type: 3}
        lutBuilderLdrPS: {fileID: 4800000, guid: 65df88701913c224d95fc554db28381a, type: 3}
        lutBuilderHdrPS: {fileID: 4800000, guid: ec9fec698a3456d4fb18cf8bacb7a2bc, type: 3}
        bloomPS: {fileID: 4800000, guid: 5f1864addb451f54bae8c86d230f736e, type: 3}
        temporalAntialiasingPS: {fileID: 4800000, guid: 9c70c1a35ff15f340b38ea84842358bf, type: 3}
        LensFlareDataDrivenPS: {fileID: 4800000, guid: 6cda457ac28612740adb23da5d39ea92, type: 3}
        LensFlareScreenSpacePS: {fileID: 4800000, guid: 701880fecb344ea4c9cd0db3407ab287, type: 3}
        scalingSetupPS: {fileID: 4800000, guid: e8ee25143a34b8c4388709ea947055d1, type: 3}
        easuPS: {fileID: 4800000, guid: 562b7ae4f629f144aa97780546fce7c6, type: 3}
        uberPostPS: {fileID: 4800000, guid: e7857e9d0c934dc4f83f270f8447b006, type: 3}
        finalPostPassPS: {fileID: 4800000, guid: c49e63ed1bbcb334780a3bd19dfed403, type: 3}
        m_ShaderResourcesVersion: 0
    - rid: 8716090808467648
      type: {class: UniversalRendererResources, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        m_Version: 0
        m_CopyDepthPS: {fileID: 4800000, guid: d6dae50ee9e1bfa4db75f19f99355220, type: 3}
        m_CameraMotionVector: {fileID: 4800000, guid: c56b7e0d4c7cb484e959caeeedae9bbf, type: 3}
        m_StencilDeferredPS: {fileID: 4800000, guid: e9155b26e1bc55942a41e518703fe304, type: 3}
        m_ClusterDeferred: {fileID: 4800000, guid: 222cce62363a44a380c36bf03b392608, type: 3}
        m_StencilDitherMaskSeedPS: {fileID: 4800000, guid: 8c3ee818f2efa514c889881ccb2e95a2, type: 3}
        m_DBufferClear: {fileID: 4800000, guid: f056d8bd2a1c7e44e9729144b4c70395, type: 3}
    - rid: 8716090808467649
      type: {class: UniversalRenderPipelineEditorMaterials, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        m_DefaultMaterial: {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
        m_DefaultParticleMaterial: {fileID: 2100000, guid: e823cd5b5d27c0f4b8256e7c12ee3e6d, type: 2}
        m_DefaultLineMaterial: {fileID: 2100000, guid: e823cd5b5d27c0f4b8256e7c12ee3e6d, type: 2}
        m_DefaultTerrainMaterial: {fileID: 2100000, guid: 594ea882c5a793440b60ff72d896021e, type: 2}
        m_DefaultDecalMaterial: {fileID: 2100000, guid: 31d0dcc6f2dd4e4408d18036a2c93862, type: 2}
        m_DefaultSpriteMaterial: {fileID: 2100000, guid: 9dfc825aed78fcd4ba02077103263b40, type: 2}
    - rid: 8716090808467650
      type: {class: ScreenSpaceAmbientOcclusionPersistentResources, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        m_Shader: {fileID: 4800000, guid: 0849e84e3d62649e8882e9d6f056a017, type: 3}
        m_Version: 0
    - rid: 8716090808467651
      type: {class: UniversalRenderPipelineEditorAssets, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        m_DefaultSettingsVolumeProfile: {fileID: 11400000, guid: eda47df5b85f4f249abf7abd73db2cb2, type: 2}
    - rid: 8716090808467652
      type: {class: UniversalRenderPipelineEditorShaders, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        m_AutodeskInteractive: {fileID: 4800000, guid: 0e9d5a909a1f7e84882a534d0d11e49f, type: 3}
        m_AutodeskInteractiveTransparent: {fileID: 4800000, guid: 5c81372d981403744adbdda4433c9c11, type: 3}
        m_AutodeskInteractiveMasked: {fileID: 4800000, guid: 80aa867ac363ac043847b06ad71604cd, type: 3}
        m_TerrainDetailLit: {fileID: 4800000, guid: f6783ab646d374f94b199774402a5144, type: 3}
        m_TerrainDetailGrassBillboard: {fileID: 4800000, guid: 29868e73b638e48ca99a19ea58c48d90, type: 3}
        m_TerrainDetailGrass: {fileID: 4800000, guid: e507fdfead5ca47e8b9a768b51c291a1, type: 3}
        m_DefaultSpeedTree7Shader: {fileID: 4800000, guid: 0f4122b9a743b744abe2fb6a0a88868b, type: 3}
        m_DefaultSpeedTree8Shader: {fileID: -6465566751694194690, guid: 9920c1f1781549a46ba081a2a15a16ec, type: 3}
        m_DefaultSpeedTree9Shader: {fileID: -6465566751694194690, guid: cbd3e1cc4ae141c42a30e33b4d666a61, type: 3}
    - rid: 8716090808467653
      type: {class: URPShaderStrippingSetting, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        m_Version: 0
        m_StripUnusedPostProcessingVariants: 0
        m_StripUnusedVariants: 1
        m_StripScreenCoordOverrideVariants: 1
    - rid: 8716090808467654
      type: {class: URPDefaultVolumeProfileSettings, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        m_Version: 0
        m_VolumeProfile: {fileID: 11400000, guid: 38bd653da66e6724fbb0199e76451566, type: 2}
    - rid: 8716090808467655
      type: {class: UniversalRenderPipelineRuntimeShaders, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        m_Version: 0
        m_FallbackErrorShader: {fileID: 4800000, guid: e6e9a19c3678ded42a3bc431ebef7dbd, type: 3}
        m_BlitHDROverlay: {fileID: 4800000, guid: a89bee29cffa951418fc1e2da94d1959, type: 3}
        m_CoreBlitPS: {fileID: 4800000, guid: 93446b5c5339d4f00b85c159e1159b7c, type: 3}
        m_CoreBlitColorAndDepthPS: {fileID: 4800000, guid: d104b2fc1ca6445babb8e90b0758136b, type: 3}
        m_SamplingPS: {fileID: 4800000, guid: 04c410c9937594faa893a11dceb85f7e, type: 3}
        m_TerrainDetailLit: {fileID: 4800000, guid: f6783ab646d374f94b199774402a5144, type: 3}
        m_TerrainDetailGrassBillboard: {fileID: 4800000, guid: 29868e73b638e48ca99a19ea58c48d90, type: 3}
        m_TerrainDetailGrass: {fileID: 4800000, guid: e507fdfead5ca47e8b9a768b51c291a1, type: 3}
    - rid: 8716090808467656
      type: {class: ScreenSpaceAmbientOcclusionDynamicResources, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        m_BlueNoise256Textures:
        - {fileID: 2800000, guid: 36f118343fc974119bee3d09e2111500, type: 3}
        - {fileID: 2800000, guid: 4b7b083e6b6734e8bb2838b0b50a0bc8, type: 3}
        - {fileID: 2800000, guid: c06cc21c692f94f5fb5206247191eeee, type: 3}
        - {fileID: 2800000, guid: cb76dd40fa7654f9587f6a344f125c9a, type: 3}
        - {fileID: 2800000, guid: e32226222ff144b24bf3a5a451de54bc, type: 3}
        - {fileID: 2800000, guid: 3302065f671a8450b82c9ddf07426f3a, type: 3}
        - {fileID: 2800000, guid: 56a77a3e8d64f47b6afe9e3c95cb57d5, type: 3}
        m_Version: 0
    - rid: 8716090808467657
      type: {class: UniversalRenderPipelineDebugShaders, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        m_DebugReplacementPS: {fileID: 4800000, guid: cf852408f2e174538bcd9b7fda1c5ae7, type: 3}
        m_HdrDebugViewPS: {fileID: 4800000, guid: 573620ae32aec764abd4d728906d2587, type: 3}
        m_ProbeVolumeSamplingDebugComputeShader: {fileID: 7200000, guid: 53626a513ea68ce47b59dc1299fe3959, type: 3}
    - rid: 8716090808467658
      type: {class: UniversalRenderPipelineRuntimeTextures, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        m_Version: 1
        m_BlueNoise64LTex: {fileID: 2800000, guid: e3d24661c1e055f45a7560c033dbb837, type: 3}
        m_BayerMatrixTex: {fileID: 2800000, guid: f9ee4ed84c1d10c49aabb9b210b0fc44, type: 3}
        m_DebugFontTex: {fileID: 2800000, guid: 26a413214480ef144b2915d6ff4d0beb, type: 3}
    - rid: 8716090808467659
      type: {class: RenderGraphSettings, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        m_Version: 0
        m_EnableRenderCompatibilityMode: 1
    - rid: 8716090808467660
      type: {class: UniversalRenderPipelineRuntimeXRResources, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        m_xrOcclusionMeshPS: {fileID: 4800000, guid: 4431b1f1f743fbf4eb310a967890cbea, type: 3}
        m_xrMirrorViewPS: {fileID: 4800000, guid: d5a307c014552314b9f560906d708772, type: 3}
        m_xrMotionVector: {fileID: 4800000, guid: f89aac1e4f84468418fe30e611dff395, type: 3}
    - rid: 8716090808467661
      type: {class: PostProcessData/TextureResources, ns: UnityEngine.Rendering.Universal, asm: Unity.RenderPipelines.Universal.Runtime}
      data:
        blueNoise16LTex:
        - {fileID: 2800000, guid: 81200413a40918d4d8702e94db29911c, type: 3}
        - {fileID: 2800000, guid: d50c5e07c9911a74982bddf7f3075e7b, type: 3}
        - {fileID: 2800000, guid: 1134690bf9216164dbc75050e35b7900, type: 3}
        - {fileID: 2800000, guid: 7ce2118f74614a94aa8a0cdf2e6062c3, type: 3}
        - {fileID: 2800000, guid: 2ca97df9d1801e84a8a8f2c53cb744f0, type: 3}
        - {fileID: 2800000, guid: e63eef8f54aa9dc4da9a5ac094b503b5, type: 3}
        - {fileID: 2800000, guid: 39451254daebd6d40b52899c1f1c0c1b, type: 3}
        - {fileID: 2800000, guid: c94ad916058dff743b0f1c969ddbe660, type: 3}
        - {fileID: 2800000, guid: ed5ea7ce59ca8ec4f9f14bf470a30f35, type: 3}
        - {fileID: 2800000, guid: 071e954febf155243a6c81e48f452644, type: 3}
        - {fileID: 2800000, guid: 96aaab9cc247d0b4c98132159688c1af, type: 3}
        - {fileID: 2800000, guid: fc3fa8f108657e14486697c9a84ccfc5, type: 3}
        - {fileID: 2800000, guid: bfed3e498947fcb4890b7f40f54d85b9, type: 3}
        - {fileID: 2800000, guid: d512512f4af60a442ab3458489412954, type: 3}
        - {fileID: 2800000, guid: 47a45908f6db0cb44a0d5e961143afec, type: 3}
        - {fileID: 2800000, guid: 4dcc0502f8586f941b5c4a66717205e8, type: 3}
        - {fileID: 2800000, guid: 9d92991794bb5864c8085468b97aa067, type: 3}
        - {fileID: 2800000, guid: 14381521ff11cb74abe3fe65401c23be, type: 3}
        - {fileID: 2800000, guid: d36f0fe53425e08499a2333cf423634c, type: 3}
        - {fileID: 2800000, guid: d4044ea2490d63b43aa1765f8efbf8a9, type: 3}
        - {fileID: 2800000, guid: c9bd74624d8070f429e3f46d161f9204, type: 3}
        - {fileID: 2800000, guid: d5c9b274310e5524ebe32a4e4da3df1f, type: 3}
        - {fileID: 2800000, guid: f69770e54f2823f43badf77916acad83, type: 3}
        - {fileID: 2800000, guid: 10b6c6d22e73dea46a8ab36b6eebd629, type: 3}
        - {fileID: 2800000, guid: a2ec5cbf5a9b64345ad3fab0912ddf7b, type: 3}
        - {fileID: 2800000, guid: 1c3c6d69a645b804fa232004b96b7ad3, type: 3}
        - {fileID: 2800000, guid: d18a24d7b4ed50f4387993566d9d3ae2, type: 3}
        - {fileID: 2800000, guid: c989e1ed85cf7154caa922fec53e6af6, type: 3}
        - {fileID: 2800000, guid: ff47e5a0f105eb34883b973e51f4db62, type: 3}
        - {fileID: 2800000, guid: fa042edbfc40fbd4bad0ab9d505b1223, type: 3}
        - {fileID: 2800000, guid: 896d9004736809c4fb5973b7c12eb8b9, type: 3}
        - {fileID: 2800000, guid: 179f794063d2a66478e6e726f84a65bc, type: 3}
        filmGrainTex:
        - {fileID: 2800000, guid: 654c582f7f8a5a14dbd7d119cbde215d, type: 3}
        - {fileID: 2800000, guid: dd77ffd079630404e879388999033049, type: 3}
        - {fileID: 2800000, guid: 1097e90e1306e26439701489f391a6c0, type: 3}
        - {fileID: 2800000, guid: f0b67500f7fad3b4c9f2b13e8f41ba6e, type: 3}
        - {fileID: 2800000, guid: 9930fb4528622b34687b00bbe6883de7, type: 3}
        - {fileID: 2800000, guid: bd9e8c758250ef449a4b4bfaad7a2133, type: 3}
        - {fileID: 2800000, guid: 510a2f57334933e4a8dbabe4c30204e4, type: 3}
        - {fileID: 2800000, guid: b4db8180660810945bf8d55ab44352ad, type: 3}
        - {fileID: 2800000, guid: fd2fd78b392986e42a12df2177d3b89c, type: 3}
        - {fileID: 2800000, guid: 5cdee82a77d13994f83b8fdabed7c301, type: 3}
        smaaAreaTex: {fileID: 2800000, guid: d1f1048909d55cd4fa1126ab998f617e, type: 3}
        smaaSearchTex: {fileID: 2800000, guid: 51eee22c2a633ef4aada830eed57c3fd, type: 3}
        m_TexturesResourcesVersion: 0
    - rid: 8716090808467662
      type: {class: GPUResidentDrawerResources, ns: UnityEngine.Rendering, asm: Unity.RenderPipelines.GPUDriven.Runtime}
      data:
        m_Version: 0
        m_InstanceDataBufferCopyKernels: {fileID: 7200000, guid: f984aeb540ded8b4fbb8a2047ab5b2e2, type: 3}
        m_InstanceDataBufferUploadKernels: {fileID: 7200000, guid: 53864816eb00f2343b60e1a2c5a262ef, type: 3}
        m_TransformUpdaterKernels: {fileID: 7200000, guid: 2a567b9b2733f8d47a700c3c85bed75b, type: 3}
        m_WindDataUpdaterKernels: {fileID: 7200000, guid: fde76746e4fd0ed418c224f6b4084114, type: 3}
        m_OccluderDepthPyramidKernels: {fileID: 7200000, guid: 08b2b5fb307b0d249860612774a987da, type: 3}
        m_InstanceOcclusionCullingKernels: {fileID: 7200000, guid: f6d223acabc2f974795a5a7864b50e6c, type: 3}
        m_OcclusionCullingDebugKernels: {fileID: 7200000, guid: b23e766bcf50ca4438ef186b174557df, type: 3}
        m_DebugOcclusionTestPS: {fileID: 4800000, guid: d3f0849180c2d0944bc71060693df100, type: 3}
        m_DebugOccluderPS: {fileID: 4800000, guid: b3c92426a88625841ab15ca6a7917248, type: 3}
    - rid: 8716090808467663
      type: {class: RenderGraphGlobalSettings, ns: UnityEngine.Rendering, asm: Unity.RenderPipelines.Core.Runtime}
      data:
        m_version: 0
        m_EnableCompilationCaching: 1
        m_EnableValidityChecks: 1
    - rid: 8716090808467664
      type: {class: ProbeVolumeRuntimeResources, ns: UnityEngine.Rendering, asm: Unity.RenderPipelines.Core.Runtime}
      data:
        m_Version: 1
        probeVolumeBlendStatesCS: {fileID: 7200000, guid: a3f7b8c99de28a94684cb1daebeccf5d, type: 3}
        probeVolumeUploadDataCS: {fileID: 7200000, guid: 0951de5992461754fa73650732c4954c, type: 3}
        probeVolumeUploadDataL2CS: {fileID: 7200000, guid: 6196f34ed825db14b81fb3eb0ea8d931, type: 3}
    - rid: 8716090808467665
      type: {class: IncludeAdditionalRPAssets, ns: UnityEngine.Rendering, asm: Unity.RenderPipelines.Core.Runtime}
      data:
        m_version: 0
        m_IncludeReferencedInScenes: 0
        m_IncludeAssetsByLabel: 0
        m_LabelToInclude: 
    - rid: 8716090808467666
      type: {class: ProbeVolumeBakingResources, ns: UnityEngine.Rendering, asm: Unity.RenderPipelines.Core.Runtime}
      data:
        m_Version: 1
        dilationShader: {fileID: 7200000, guid: 6bb382f7de370af41b775f54182e491d, type: 3}
        subdivideSceneCS: {fileID: 7200000, guid: bb86f1f0af829fd45b2ebddda1245c22, type: 3}
        voxelizeSceneShader: {fileID: 4800000, guid: c8b6a681c7b4e2e4785ffab093907f9e, type: 3}
        traceVirtualOffsetCS: {fileID: -6772857160820960102, guid: ff2cbab5da58bf04d82c5f34037ed123, type: 3}
        traceVirtualOffsetRT: {fileID: -5126288278712620388, guid: ff2cbab5da58bf04d82c5f34037ed123, type: 3}
        skyOcclusionCS: {fileID: -6772857160820960102, guid: 5a2a534753fbdb44e96c3c78b5a6999d, type: 3}
        skyOcclusionRT: {fileID: -5126288278712620388, guid: 5a2a534753fbdb44e96c3c78b5a6999d, type: 3}
        renderingLayerCS: {fileID: -6772857160820960102, guid: 94a070d33e408384bafc1dea4a565df9, type: 3}
        renderingLayerRT: {fileID: -5126288278712620388, guid: 94a070d33e408384bafc1dea4a565df9, type: 3}
    - rid: 8716090808467667
      type: {class: STP/RuntimeResources, ns: UnityEngine.Rendering, asm: Unity.RenderPipelines.Core.Runtime}
      data:
        m_setupCS: {fileID: 7200000, guid: 33be2e9a5506b2843bdb2bdff9cad5e1, type: 3}
        m_preTaaCS: {fileID: 7200000, guid: a679dba8ec4d9ce45884a270b0e22dda, type: 3}
        m_taaCS: {fileID: 7200000, guid: 3923900e2b41b5e47bc25bfdcbcdc9e6, type: 3}
    - rid: 8716090808467668
      type: {class: ProbeVolumeDebugResources, ns: UnityEngine.Rendering, asm: Unity.RenderPipelines.Core.Runtime}
      data:
        m_Version: 1
        probeVolumeDebugShader: {fileID: 4800000, guid: 3b21275fd12d65f49babb5286f040f2d, type: 3}
        probeVolumeFragmentationDebugShader: {fileID: 4800000, guid: 3a80877c579b9144ebdcc6d923bca303, type: 3}
        probeVolumeSamplingDebugShader: {fileID: 4800000, guid: bf54e6528c79a224e96346799064c393, type: 3}
        probeVolumeOffsetDebugShader: {fileID: 4800000, guid: db8bd7436dc2c5f4c92655307d198381, type: 3}
        probeSamplingDebugMesh: {fileID: -3555484719484374845, guid: 20be25aac4e22ee49a7db76fb3df6de2, type: 3}
        numbersDisplayTex: {fileID: 2800000, guid: 73fe53b428c5b3440b7e87ee830b608a, type: 3}
    - rid: 8716090808467669
      type: {class: ProbeVolumeGlobalSettings, ns: UnityEngine.Rendering, asm: Unity.RenderPipelines.Core.Runtime}
      data:
        m_Version: 1
        m_ProbeVolumeDisableStreamingAssets: 0
    - rid: 8716090808467670
      type: {class: LightmapSamplingSettings, ns: UnityEngine.Rendering, asm: Unity.RenderPipelines.Core.Runtime}
      data:
        m_Version: 1
        m_UseBicubicLightmapSampling: 0
    - rid: 8716090808467671
      type: {class: ShaderStrippingSetting, ns: UnityEngine.Rendering, asm: Unity.RenderPipelines.Core.Runtime}
      data:
        m_Version: 0
        m_ExportShaderVariants: 1
        m_ShaderVariantLogLevel: 0
        m_StripRuntimeDebugShaders: 1
    - rid: 8716090808467672
      type: {class: VrsRenderPipelineRuntimeResources, ns: UnityEngine.Rendering, asm: Unity.RenderPipelines.Core.Runtime}
      data:
        m_TextureComputeShader: {fileID: 7200000, guid: cacb30de6c40c7444bbc78cb0a81fd2a, type: 3}
        m_VisualizationShader: {fileID: 4800000, guid: 620b55b8040a88d468e94abe55bed5ba, type: 3}
        m_VisualizationLookupTable:
          m_Data:
          - {r: 1, g: 0, b: 0, a: 1}
          - {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          - {r: 1, g: 1, b: 1, a: 1}
          - {r: 0, g: 1, b: 0, a: 1}
          - {r: 0.75, g: 0.75, b: 0, a: 1}
          - {r: 0, g: 0.75, b: 0.55, a: 1}
          - {r: 0.5, g: 0, b: 0.5, a: 1}
          - {r: 0.5, g: 0.5, b: 0.5, a: 1}
          - {r: 0, g: 0, b: 1, a: 1}
        m_ConversionLookupTable:
          m_Data:
          - {r: 1, g: 0, b: 0, a: 1}
          - {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          - {r: 1, g: 1, b: 1, a: 1}
          - {r: 0, g: 1, b: 0, a: 1}
          - {r: 0.75, g: 0.75, b: 0, a: 1}
          - {r: 0, g: 0.75, b: 0.55, a: 1}
          - {r: 0.5, g: 0, b: 0.5, a: 1}
          - {r: 0.5, g: 0.5, b: 0.5, a: 1}
          - {r: 0, g: 0, b: 1, a: 1}
    - rid: 8716090808467673
      type: {class: RenderGraphUtilsResources, ns: UnityEngine.Rendering.RenderGraphModule.Util, asm: Unity.RenderPipelines.Core.Runtime}
      data:
        m_Version: 0
        m_CoreCopyPS: {fileID: 4800000, guid: 12dc59547ea167a4ab435097dd0f9add, type: 3}
