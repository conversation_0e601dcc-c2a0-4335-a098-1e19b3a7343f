@echo off
echo ========================================
echo Unity 6 AI Teammate Bot - Final Validation
echo ========================================
echo.

echo Checking Unity 6 project structure...
echo.

echo Core Scripts:
if exist "Assets\Scripts\PlayerController.cs" (
    echo ✓ PlayerController.cs
) else (
    echo ✗ PlayerController.cs - MISSING
)

if exist "Assets\Scripts\TeammateAI.cs" (
    echo ✓ TeammateAI.cs
) else (
    echo ✗ TeammateAI.cs - MISSING
)

if exist "Assets\Scripts\InviteBotUI.cs" (
    echo ✓ InviteBotUI.cs
) else (
    echo ✗ InviteBotUI.cs - MISSING
)

if exist "Assets\Scripts\VoiceFeedbackSystem.cs" (
    echo ✓ VoiceFeedbackSystem.cs
) else (
    echo ✗ VoiceFeedbackSystem.cs - MISSING
)

if exist "Assets\Scripts\SubtitleUI.cs" (
    echo ✓ SubtitleUI.cs
) else (
    echo ✗ SubtitleUI.cs - MISSING
)

if exist "Assets\Scripts\EnemyHealth.cs" (
    echo ✓ EnemyHealth.cs
) else (
    echo ✗ EnemyHealth.cs - MISSING
)

if exist "Assets\Scripts\ReviveSystem.cs" (
    echo ✓ ReviveSystem.cs
) else (
    echo ✗ ReviveSystem.cs - MISSING
)

if exist "Assets\Scripts\EnemyAI.cs" (
    echo ✓ EnemyAI.cs
) else (
    echo ✗ EnemyAI.cs - MISSING
)

if exist "Assets\Scripts\GameManager.cs" (
    echo ✓ GameManager.cs
) else (
    echo ✗ GameManager.cs - MISSING
)

if exist "Assets\Scripts\SceneSetup.cs" (
    echo ✓ SceneSetup.cs
) else (
    echo ✗ SceneSetup.cs - MISSING
)

if exist "Assets\Scripts\PrefabBuilder.cs" (
    echo ✓ PrefabBuilder.cs
) else (
    echo ✗ PrefabBuilder.cs - MISSING
)

if exist "Assets\Scripts\AITeammateTest.cs" (
    echo ✓ AITeammateTest.cs
) else (
    echo ✗ AITeammateTest.cs - MISSING
)

if exist "Assets\Scripts\CompilationTest.cs" (
    echo ✓ CompilationTest.cs
) else (
    echo ✗ CompilationTest.cs - MISSING
)

echo.
echo Unity 6 Specific Files:
if exist "ProjectSettings\ProjectVersion.txt" (
    echo ✓ Unity 6000.1.6f1 Project Version
) else (
    echo ✗ Project Version - MISSING
)

if exist "Packages\manifest.json" (
    echo ✓ Unity 6 Package Manifest
) else (
    echo ✗ Package Manifest - MISSING
)

if exist "Assets\Scenes\TestArena_Unity6.unity" (
    echo ✓ Unity 6 Test Scene
) else (
    echo ✗ Unity 6 Test Scene - MISSING
)

echo.
echo Prefabs:
if exist "Assets\Prefabs\TeammateBot.prefab" (
    echo ✓ TeammateBot.prefab
) else (
    echo ✗ TeammateBot.prefab - MISSING
)

if exist "Assets\Prefabs\Player.prefab" (
    echo ✓ Player.prefab
) else (
    echo ✗ Player.prefab - MISSING
)

if exist "Assets\Prefabs\EnhancedEnemy.prefab" (
    echo ✓ EnhancedEnemy.prefab
) else (
    echo ✗ EnhancedEnemy.prefab - MISSING
)

echo.
echo Documentation:
if exist "README.md" (
    echo ✓ README.md
) else (
    echo ✗ README.md - MISSING
)

if exist "UNITY6_SETUP_GUIDE.md" (
    echo ✓ UNITY6_SETUP_GUIDE.md
) else (
    echo ✗ UNITY6_SETUP_GUIDE.md - MISSING
)

if exist "PREFAB_SETUP_GUIDE.md" (
    echo ✓ PREFAB_SETUP_GUIDE.md
) else (
    echo ✗ PREFAB_SETUP_GUIDE.md - MISSING
)

if exist "DEVELOPMENT_GUIDE.md" (
    echo ✓ DEVELOPMENT_GUIDE.md
) else (
    echo ✗ DEVELOPMENT_GUIDE.md - MISSING
)

echo.
echo ========================================
echo UNITY 6 AI TEAMMATE BOT - READY!
echo ========================================
echo.
echo Professional Features Included:
echo ✓ Runtime AI Invite System
echo ✓ Advanced Tactical AI Decision Tree
echo ✓ Voice Feedback with Subtitles
echo ✓ Enhanced Enemy Health System
echo ✓ Professional UI Interface
echo ✓ Cover and Flanking Behaviors
echo ✓ Smart Threat Assessment
echo ✓ Squad Management (up to 3 AI)
echo.
echo Next Steps:
echo 1. Open Unity Hub
echo 2. Add this project folder
echo 3. Open with Unity 6000.1.6f1
echo 4. Open TestArena_Unity6.unity scene
echo 5. Press Play and click "Invite AI Teammate"
echo 6. Test with F1-F9 keys and 'I' for invite
echo.
echo Controls:
echo - Click "Invite AI Teammate" or Press 'I'
echo - WASD: Move player
echo - Y: Down player (test revive)
echo - F1-F9: Individual behavior tests
echo - F10: Runtime compilation test
echo.
echo Your professional AI teammate system is ready!
echo.
pause
