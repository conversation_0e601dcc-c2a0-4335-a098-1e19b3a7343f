@echo off
echo ========================================
echo Unity AI Teammate Bot - Prefab Builder
echo ========================================
echo.

echo This script will guide you through building the AI Teammate Bot prefabs in Unity.
echo.

echo STEP 1: Open Unity Project
echo -------------------------
echo 1. Open Unity Hub
echo 2. Add this project folder: %CD%
echo 3. Open with Unity 2022.3 LTS or newer
echo 4. Wait for project to load completely
echo.

echo STEP 2: Build Prefabs Automatically
echo ----------------------------------
echo 1. In Unity, create an empty GameObject in the scene
echo 2. Add the "PrefabBuilder.cs" script to it
echo 3. In the inspector, check "Create Prefab On Start"
echo 4. Press Play - prefabs will be created automatically!
echo.
echo OR manually:
echo 1. Right-click the PrefabBuilder component
echo 2. Select "Create Teammate Bo<PERSON> Prefab"
echo 3. Select "Create Player Prefab"
echo.

echo STEP 3: Save as Prefabs
echo ----------------------
echo 1. Drag "TeammateBot" from scene to Assets/Prefabs/ folder
echo 2. Drag "Player" from scene to Assets/Prefabs/ folder
echo 3. Delete the GameObjects from scene (keep prefabs in folder)
echo.

echo STEP 4: Test Your Prefabs
echo ------------------------
echo 1. Drag Player prefab into scene
echo 2. Drag TeammateBot prefab into scene
echo 3. Assign Player reference in TeammateAI component
echo 4. Bake NavMesh (Window > AI > Navigation > Bake)
echo 5. Press Play and test!
echo.

echo PREFAB COMPONENTS INCLUDED:
echo ==========================
echo.
echo TeammateBot Prefab:
echo ✓ NavMeshAgent (configured for optimal pathfinding)
echo ✓ TeammateAI.cs (complete AI state machine)
echo ✓ BotBody with MainBody, Head, Weapon visuals
echo ✓ FirePoint for combat targeting
echo ✓ Particle effects for revive and combat
echo ✓ Green color scheme for easy identification
echo.
echo Player Prefab:
echo ✓ CharacterController (FPS movement)
echo ✓ PlayerController.cs (movement and health)
echo ✓ ReviveSystem.cs (down/revive mechanics)
echo ✓ PlayerCamera with AudioListener
echo ✓ PlayerBody visual representation
echo ✓ Blue color scheme for player identification
echo.

echo TEST CONTROLS:
echo ==============
echo WASD - Move player
echo Mouse - Look around
echo T - Damage player (25 HP)
echo Y - Down player (AI should revive)
echo F1-F9 - Individual behavior tests
echo.

echo TROUBLESHOOTING:
echo ===============
echo - If AI doesn't move: Check NavMesh is baked
echo - If AI doesn't follow: Assign Player reference
echo - If revive doesn't work: Check ReviveSystem component
echo - If combat fails: Verify enemy layer is set to 8
echo.

echo Ready to build your AI teammate prefabs!
echo Press any key to open the project folder...
pause >nul

explorer .
