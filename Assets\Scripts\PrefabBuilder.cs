using UnityEngine;
using UnityEngine.AI;

/// <summary>
/// Automated prefab builder for AI Teammate Bot
/// This script creates the complete teammate prefab with all components configured
/// </summary>
public class PrefabBuilder : MonoBehaviour
{
    [Header("Prefab Creation Settings")]
    public bool createPrefabOnStart = false;
    public string prefabName = "TeammateBot";
    
    [Header("Visual Settings")]
    public Material botMaterial;
    public Color botColor = Color.green;
    public Vector3 botScale = new Vector3(1f, 1.5f, 1f);
    
    [Header("AI Settings")]
    public float moveSpeed = 3.5f;
    public float angularSpeed = 120f;
    public float stoppingDistance = 1.5f;
    public float followDistance = 4f;
    public float reviveDistance = 2f;
    
    void Start()
    {
        if (createPrefabOnStart)
        {
            CreateTeammateBotPrefab();
        }
    }
    
    [ContextMenu("Create Teammate Bot Prefab")]
    public void CreateTeammateBotPrefab()
    {
        Debug.Log("Creating Teammate Bot Prefab...");
        
        // Create main GameObject
        GameObject teammateBot = new GameObject(prefabName);
        teammateBot.transform.position = Vector3.zero;
        
        // Add NavMeshAgent
        NavMeshAgent agent = teammateBot.AddComponent<NavMeshAgent>();
        ConfigureNavMeshAgent(agent);
        
        // Add TeammateAI script
        TeammateAI ai = teammateBot.AddComponent<TeammateAI>();
        ConfigureTeammateAI(ai);
        
        // Create visual body
        GameObject botBody = CreateBotBody(teammateBot.transform);
        
        // Create fire point for combat
        GameObject firePoint = CreateFirePoint(teammateBot.transform);
        ai.firePoint = firePoint.transform;
        
        // Create revive effect
        GameObject reviveEffect = CreateReviveEffect(teammateBot.transform);
        
        // Create particle systems
        CreateParticleSystems(teammateBot.transform);
        
        // Save as prefab
        SaveAsPrefab(teammateBot);
        
        Debug.Log($"✅ Teammate Bot Prefab '{prefabName}' created successfully!");
    }
    
    void ConfigureNavMeshAgent(NavMeshAgent agent)
    {
        agent.speed = moveSpeed;
        agent.angularSpeed = angularSpeed;
        agent.stoppingDistance = stoppingDistance;
        agent.radius = 0.5f;
        agent.height = 2f;
        agent.baseOffset = 0f;
        agent.acceleration = 8f;
        agent.obstacleAvoidanceType = ObstacleAvoidanceType.HighQualityObstacleAvoidance;
        
        Debug.Log("✅ NavMeshAgent configured");
    }
    
    void ConfigureTeammateAI(TeammateAI ai)
    {
        ai.followDistance = followDistance;
        ai.reviveDistance = reviveDistance;
        ai.reviveTime = 3f;
        ai.attackRange = 15f;
        ai.lootRange = 5f;
        ai.damage = 25f;
        ai.fireRate = 2f;
        
        // Find player automatically
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player != null)
        {
            ai.player = player;
            Debug.Log("✅ Player reference assigned to AI");
        }
        else
        {
            Debug.LogWarning("⚠️ Player not found - assign manually in inspector");
        }
        
        Debug.Log("✅ TeammateAI configured");
    }
    
    GameObject CreateBotBody(Transform parent)
    {
        // Create body container
        GameObject bodyContainer = new GameObject("BotBody");
        bodyContainer.transform.SetParent(parent);
        bodyContainer.transform.localPosition = Vector3.zero;
        bodyContainer.transform.localRotation = Quaternion.identity;
        
        // Create main body (capsule)
        GameObject mainBody = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        mainBody.name = "MainBody";
        mainBody.transform.SetParent(bodyContainer.transform);
        mainBody.transform.localPosition = Vector3.up;
        mainBody.transform.localScale = botScale;
        
        // Remove collider (NavMeshAgent handles collision)
        Destroy(mainBody.GetComponent<Collider>());
        
        // Set material and color
        Renderer bodyRenderer = mainBody.GetComponent<Renderer>();
        if (bodyRenderer != null)
        {
            if (botMaterial != null)
            {
                bodyRenderer.material = botMaterial;
            }
            else
            {
                bodyRenderer.material.color = botColor;
            }
        }
        
        // Create head indicator
        GameObject head = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        head.name = "Head";
        head.transform.SetParent(bodyContainer.transform);
        head.transform.localPosition = new Vector3(0f, 2.2f, 0f);
        head.transform.localScale = Vector3.one * 0.4f;
        
        // Remove head collider
        Destroy(head.GetComponent<Collider>());
        
        // Make head slightly different color
        Renderer headRenderer = head.GetComponent<Renderer>();
        if (headRenderer != null)
        {
            headRenderer.material.color = botColor * 1.2f;
        }
        
        // Create weapon indicator (simple cube)
        GameObject weapon = GameObject.CreatePrimitive(PrimitiveType.Cube);
        weapon.name = "Weapon";
        weapon.transform.SetParent(bodyContainer.transform);
        weapon.transform.localPosition = new Vector3(0.3f, 1.2f, 0.5f);
        weapon.transform.localScale = new Vector3(0.1f, 0.1f, 0.8f);
        weapon.transform.localRotation = Quaternion.Euler(0f, 0f, 0f);
        
        // Remove weapon collider
        Destroy(weapon.GetComponent<Collider>());
        
        // Make weapon dark
        Renderer weaponRenderer = weapon.GetComponent<Renderer>();
        if (weaponRenderer != null)
        {
            weaponRenderer.material.color = Color.black;
        }
        
        Debug.Log("✅ Bot body created with head and weapon");
        return bodyContainer;
    }
    
    GameObject CreateFirePoint(Transform parent)
    {
        GameObject firePoint = new GameObject("FirePoint");
        firePoint.transform.SetParent(parent);
        firePoint.transform.localPosition = new Vector3(0.3f, 1.2f, 0.9f);
        firePoint.transform.localRotation = Quaternion.identity;
        
        Debug.Log("✅ Fire point created");
        return firePoint;
    }
    
    GameObject CreateReviveEffect(Transform parent)
    {
        GameObject reviveEffectObj = new GameObject("ReviveEffect");
        reviveEffectObj.transform.SetParent(parent);
        reviveEffectObj.transform.localPosition = Vector3.up;
        
        ParticleSystem particles = reviveEffectObj.AddComponent<ParticleSystem>();
        
        // Configure particle system for revive effect
        var main = particles.main;
        main.startLifetime = 2f;
        main.startSpeed = 2f;
        main.startSize = 0.2f;
        main.startColor = Color.green;
        main.maxParticles = 50;
        
        var emission = particles.emission;
        emission.enabled = false; // Only emit when reviving
        emission.SetBursts(new ParticleSystem.Burst[]
        {
            new ParticleSystem.Burst(0f, 30)
        });
        
        var shape = particles.shape;
        shape.shapeType = ParticleSystemShapeType.Circle;
        shape.radius = 1f;
        
        var velocityOverLifetime = particles.velocityOverLifetime;
        velocityOverLifetime.enabled = true;
        velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
        velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(3f);
        
        Debug.Log("✅ Revive effect created");
        return reviveEffectObj;
    }
    
    void CreateParticleSystems(Transform parent)
    {
        // Create muzzle flash effect
        GameObject muzzleFlash = new GameObject("MuzzleFlash");
        muzzleFlash.transform.SetParent(parent);
        muzzleFlash.transform.localPosition = new Vector3(0.3f, 1.2f, 0.9f);
        
        ParticleSystem muzzleParticles = muzzleFlash.AddComponent<ParticleSystem>();
        
        var main = muzzleParticles.main;
        main.startLifetime = 0.1f;
        main.startSpeed = 5f;
        main.startSize = 0.1f;
        main.startColor = Color.yellow;
        main.maxParticles = 10;
        
        var emission = muzzleParticles.emission;
        emission.enabled = false;
        emission.SetBursts(new ParticleSystem.Burst[]
        {
            new ParticleSystem.Burst(0f, 5)
        });
        
        Debug.Log("✅ Particle systems created");
    }
    
    void SaveAsPrefab(GameObject prefabObject)
    {
        // In a real Unity environment, you would use:
        // PrefabUtility.SaveAsPrefabAsset(prefabObject, $"Assets/Prefabs/{prefabName}.prefab");
        
        // For now, we'll just position it in the scene for manual prefab creation
        prefabObject.transform.position = new Vector3(5f, 0f, 0f);
        
        Debug.Log($"✅ Prefab '{prefabName}' ready for manual saving to Assets/Prefabs/");
        Debug.Log("📝 To save as prefab: Drag the GameObject to Assets/Prefabs/ folder");
    }
    
    [ContextMenu("Create Player Prefab")]
    public void CreatePlayerPrefab()
    {
        Debug.Log("Creating Player Prefab...");
        
        GameObject player = new GameObject("Player");
        
        // Add CharacterController
        CharacterController controller = player.AddComponent<CharacterController>();
        controller.height = 2f;
        controller.radius = 0.5f;
        
        // Add PlayerController
        PlayerController playerController = player.AddComponent<PlayerController>();
        
        // Add ReviveSystem
        player.AddComponent<ReviveSystem>();
        
        // Create camera
        GameObject camera = new GameObject("PlayerCamera");
        camera.transform.SetParent(player.transform);
        camera.transform.localPosition = new Vector3(0f, 1.6f, 0f);
        
        Camera cam = camera.AddComponent<Camera>();
        camera.AddComponent<AudioListener>();
        
        playerController.playerCamera = cam;
        playerController.cameraTransform = camera.transform;
        
        // Create visual body
        GameObject playerBody = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        playerBody.name = "PlayerBody";
        playerBody.transform.SetParent(player.transform);
        playerBody.transform.localPosition = Vector3.zero;
        
        // Remove collider (CharacterController handles it)
        Destroy(playerBody.GetComponent<Collider>());
        
        // Set blue color
        Renderer playerRenderer = playerBody.GetComponent<Renderer>();
        if (playerRenderer != null)
        {
            playerRenderer.material.color = Color.blue;
        }
        
        player.transform.position = new Vector3(-5f, 0f, 0f);
        
        Debug.Log("✅ Player prefab created and ready for manual saving");
    }
}
