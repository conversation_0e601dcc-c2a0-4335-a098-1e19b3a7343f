using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

public class GameManager : MonoBehaviour
{
    [Header("Game Settings")]
    public int maxEnemies = 5;
    public float enemySpawnRadius = 20f;
    public float enemyRespawnTime = 10f;
    
    [Header("UI References")]
    public Text healthText;
    public Text stateText;
    public Text reviveProgressText;
    public Slider healthSlider;
    
    [Header("Prefabs")]
    public GameObject enemyPrefab;
    public GameObject playerPrefab;
    public GameObject teammatePrefab;
    
    [Header("Spawn Points")]
    public Transform playerSpawnPoint;
    public Transform teammateSpawnPoint;
    public Transform[] enemySpawnPoints;
    
    private PlayerController player;
    private TeammateAI teammate;
    private List<EnemyAI> activeEnemies = new List<EnemyAI>();
    private float lastEnemySpawnTime;
    
    public static GameManager Instance { get; private set; }
    
    void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        InitializeGame();
        InvokeRepeating(nameof(UpdateUI), 0f, 0.1f);
        InvokeRepeating(nameof(ManageEnemies), 0f, 1f);
    }
    
    void InitializeGame()
    {
        // Find or spawn player
        player = FindObjectOfType<PlayerController>();
        if (player == null && playerPrefab != null)
        {
            Vector3 spawnPos = playerSpawnPoint != null ? playerSpawnPoint.position : Vector3.zero;
            GameObject playerObj = Instantiate(playerPrefab, spawnPos, Quaternion.identity);
            player = playerObj.GetComponent<PlayerController>();
        }
        
        // Find or spawn teammate
        teammate = FindObjectOfType<TeammateAI>();
        if (teammate == null && teammatePrefab != null)
        {
            Vector3 spawnPos = teammateSpawnPoint != null ? teammateSpawnPoint.position : Vector3.right * 2f;
            GameObject teammateObj = Instantiate(teammatePrefab, spawnPos, Quaternion.identity);
            teammate = teammateObj.GetComponent<TeammateAI>();
        }
        
        // Set up UI
        SetupUI();
        
        Debug.Log("Game initialized successfully!");
    }
    
    void SetupUI()
    {
        // Create UI if it doesn't exist
        if (healthText == null || healthSlider == null)
        {
            CreateBasicUI();
        }
    }
    
    void CreateBasicUI()
    {
        // Create Canvas
        GameObject canvasObj = new GameObject("GameUI");
        Canvas canvas = canvasObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvasObj.AddComponent<CanvasScaler>();
        canvasObj.AddComponent<GraphicRaycaster>();
        
        // Health Text
        GameObject healthTextObj = new GameObject("HealthText");
        healthTextObj.transform.SetParent(canvasObj.transform);
        healthText = healthTextObj.AddComponent<Text>();
        healthText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        healthText.fontSize = 20;
        healthText.color = Color.white;
        
        RectTransform healthRect = healthText.GetComponent<RectTransform>();
        healthRect.anchorMin = new Vector2(0, 1);
        healthRect.anchorMax = new Vector2(0, 1);
        healthRect.anchoredPosition = new Vector2(10, -10);
        healthRect.sizeDelta = new Vector2(200, 30);
        
        // State Text
        GameObject stateTextObj = new GameObject("StateText");
        stateTextObj.transform.SetParent(canvasObj.transform);
        stateText = stateTextObj.AddComponent<Text>();
        stateText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        stateText.fontSize = 16;
        stateText.color = Color.cyan;
        
        RectTransform stateRect = stateText.GetComponent<RectTransform>();
        stateRect.anchorMin = new Vector2(0, 1);
        stateRect.anchorMax = new Vector2(0, 1);
        stateRect.anchoredPosition = new Vector2(10, -40);
        stateRect.sizeDelta = new Vector2(200, 25);
        
        // Revive Progress Text
        GameObject reviveTextObj = new GameObject("ReviveText");
        reviveTextObj.transform.SetParent(canvasObj.transform);
        reviveProgressText = reviveTextObj.AddComponent<Text>();
        reviveProgressText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        reviveProgressText.fontSize = 18;
        reviveProgressText.color = Color.green;
        
        RectTransform reviveRect = reviveProgressText.GetComponent<RectTransform>();
        reviveRect.anchorMin = new Vector2(0.5f, 0.5f);
        reviveRect.anchorMax = new Vector2(0.5f, 0.5f);
        reviveRect.anchoredPosition = new Vector2(0, 0);
        reviveRect.sizeDelta = new Vector2(300, 30);
        
        Debug.Log("Basic UI created successfully!");
    }
    
    void UpdateUI()
    {
        if (player == null) return;
        
        // Update health display
        if (healthText != null)
        {
            healthText.text = $"Health: {player.currentHealth:F0}/{player.maxHealth:F0}";

            ReviveSystem reviveSystem = player.GetReviveSystem();
            if (reviveSystem != null && reviveSystem.IsDown())
            {
                float timeLeft = reviveSystem.GetDownedTimeRemaining();
                healthText.text += $"\nDOWNED! Time left: {timeLeft:F1}s";
                healthText.color = Color.red;
            }
            else
            {
                healthText.color = Color.white;
            }
        }
        
        // Update health slider
        if (healthSlider != null)
        {
            healthSlider.value = player.GetHealthPercentage();
        }
        
        // Update AI state
        if (stateText != null && teammate != null)
        {
            stateText.text = $"AI State: {teammate.currentState}";
        }
        
        // Update revive progress
        if (reviveProgressText != null)
        {
            ReviveSystem reviveSystem = player.GetReviveSystem();
            if (reviveSystem != null && reviveSystem.IsDown() && teammate != null)
            {
                float distance = Vector3.Distance(player.transform.position, teammate.transform.position);
                if (distance <= teammate.reviveDistance && teammate.currentState == TeammateAI.AIState.Reviving)
                {
                    // Calculate progress based on revive time
                    float progress = (teammate.reviveProgress / teammate.reviveTime) * 100f;
                    reviveProgressText.text = $"Reviving... {progress:F0}%";
                    reviveProgressText.gameObject.SetActive(true);
                }
                else if (teammate.currentState == TeammateAI.AIState.Following || teammate.currentState == TeammateAI.AIState.Combat)
                {
                    reviveProgressText.text = "Teammate coming to revive...";
                    reviveProgressText.gameObject.SetActive(true);
                }
                else
                {
                    reviveProgressText.gameObject.SetActive(false);
                }
            }
            else
            {
                reviveProgressText.gameObject.SetActive(false);
            }
        }
    }
    
    void ManageEnemies()
    {
        // Remove dead enemies from list
        activeEnemies.RemoveAll(enemy => enemy == null);
        
        // Spawn new enemies if needed
        if (activeEnemies.Count < maxEnemies && Time.time >= lastEnemySpawnTime + enemyRespawnTime)
        {
            SpawnEnemy();
            lastEnemySpawnTime = Time.time;
        }
    }
    
    void SpawnEnemy()
    {
        if (enemyPrefab == null) return;
        
        Vector3 spawnPosition;
        
        // Use predefined spawn points if available
        if (enemySpawnPoints != null && enemySpawnPoints.Length > 0)
        {
            Transform spawnPoint = enemySpawnPoints[Random.Range(0, enemySpawnPoints.Length)];
            spawnPosition = spawnPoint.position;
        }
        else
        {
            // Random spawn around player
            Vector3 playerPos = player != null ? player.transform.position : Vector3.zero;
            Vector2 randomCircle = Random.insideUnitCircle * enemySpawnRadius;
            spawnPosition = playerPos + new Vector3(randomCircle.x, 0, randomCircle.y);
        }
        
        GameObject enemyObj = Instantiate(enemyPrefab, spawnPosition, Quaternion.identity);
        EnemyAI enemy = enemyObj.GetComponent<EnemyAI>();
        
        if (enemy != null)
        {
            activeEnemies.Add(enemy);
            Debug.Log($"Spawned enemy at {spawnPosition}. Active enemies: {activeEnemies.Count}");
        }
    }
    
    // Public methods for testing
    public void TestPlayerDamage()
    {
        if (player != null)
        {
            player.TakeDamage(25f);
            Debug.Log("Test: Player took 25 damage");
        }
    }
    
    public void TestPlayerDown()
    {
        if (player != null)
        {
            player.GoDown();
            Debug.Log("Test: Player forced down");
        }
    }
    
    public void TestPartyInvite()
    {
        if (teammate != null)
        {
            teammate.ReceivePartyInvite("TestPlayer");
            Debug.Log("Test: Sent party invite to AI");
        }
    }
    
    void Update()
    {
        // Test controls (remove in production)
        if (Input.GetKeyDown(KeyCode.T))
        {
            TestPlayerDamage();
        }
        
        if (Input.GetKeyDown(KeyCode.Y))
        {
            TestPlayerDown();
        }
        
        if (Input.GetKeyDown(KeyCode.U))
        {
            TestPartyInvite();
        }
        
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            Application.Quit();
        }
    }
}
