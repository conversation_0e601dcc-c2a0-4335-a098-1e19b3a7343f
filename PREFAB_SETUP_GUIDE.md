# 🧱 Unity AI Teammate Bot - Prefab Setup Guide

## 🎯 **Complete Prefab Creation Workflow**

This guide walks you through creating the **TeammateBot prefab** with all components properly configured and ready for testing.

---

## 🚀 **Method 1: Automated Prefab Creation (Recommended)**

### **Step 1: Use PrefabBuilder Script**
1. **Open Unity** and load your project
2. **Create empty GameObject** in scene
3. **Add PrefabBuilder.cs** component to it
4. **Right-click component** → **"Create Teammate Bot Prefab"**
5. **Prefab automatically created** with all components!

### **Step 2: Save as Prefab**
1. **Drag the created TeammateBot** from scene to `Assets/Prefabs/` folder
2. **Delete from scene** (keep prefab in folder)
3. **Ready to use!** 🎉

---

## 🔧 **Method 2: Manual Prefab Creation**

### **Step 1: Create Main GameObject**
```
Right-click Hierarchy → Create Empty
Rename to: "TeammateBot"
Position: (0, 0, 0)
```

### **Step 2: Add Core Components**

**NavMeshAgent Configuration:**
```
Speed: 3.5
Angular Speed: 120
Stopping Distance: 1.5
Radius: 0.5
Height: 2.0
Acceleration: 8
Obstacle Avoidance: High Quality
```

**TeammateAI Configuration:**
```
Follow Distance: 4
Revive Distance: 2
Revive Time: 3
Attack Range: 15
Loot Range: 5
Damage: 25
Fire Rate: 2
Enemy Layer: 8 (256 in bitmask)
Loot Layer: 9 (512 in bitmask)
```

### **Step 3: Create Visual Body**

**Create Body Container:**
```
Right-click TeammateBot → Create Empty
Rename to: "BotBody"
Position: (0, 0, 0)
```

**Add Main Body:**
```
Right-click BotBody → 3D Object → Capsule
Rename to: "MainBody"
Position: (0, 1, 0)
Scale: (1, 1.5, 1)
Color: Green
Remove Capsule Collider (NavMeshAgent handles collision)
```

**Add Head Indicator:**
```
Right-click BotBody → 3D Object → Sphere
Rename to: "Head"
Position: (0, 2.2, 0)
Scale: (0.4, 0.4, 0.4)
Color: Light Green
Remove Sphere Collider
```

**Add Weapon Indicator:**
```
Right-click BotBody → 3D Object → Cube
Rename to: "Weapon"
Position: (0.3, 1.2, 0.5)
Scale: (0.1, 0.1, 0.8)
Color: Black
Remove Box Collider
```

### **Step 4: Create Fire Point**
```
Right-click TeammateBot → Create Empty
Rename to: "FirePoint"
Position: (0.3, 1.2, 0.9)
Assign to TeammateAI → Fire Point field
```

### **Step 5: Add Particle Effects (Optional)**

**Revive Effect:**
```
Right-click TeammateBot → Effects → Particle System
Rename to: "ReviveEffect"
Position: (0, 1, 0)
Configure for green burst effect
Set Emission to manual (not continuous)
```

**Muzzle Flash:**
```
Right-click FirePoint → Effects → Particle System
Rename to: "MuzzleFlash"
Configure for yellow flash effect
Set Emission to manual bursts
```

### **Step 6: Final Configuration**

**Assign References:**
- TeammateAI → Player: Drag Player object from scene
- TeammateAI → Fire Point: Drag FirePoint child object
- TeammateAI → Bullet Prefab: (Optional) Assign bullet prefab

**Layer Setup:**
- TeammateBot: Layer 0 (Default)
- Ensure Enemy objects use Layer 8
- Ensure Loot objects use Layer 9

---

## 🧪 **Testing Your Prefab**

### **Quick Test Checklist:**
1. **Drag prefab** into scene
2. **Assign Player reference** in TeammateAI component
3. **Ensure NavMesh is baked** (Window > AI > Navigation > Bake)
4. **Press Play** ▶️
5. **Move player** → AI should follow
6. **Press Y** → AI should revive downed player

### **Test Controls:**
- `WASD` - Move player
- `T` - Damage player
- `Y` - Down player (test revive)
- `F1-F9` - Individual behavior tests

---

## 📋 **Prefab Component Hierarchy**

```
TeammateBot (Root)
├── NavMeshAgent
├── TeammateAI.cs
├── BotBody/
│   ├── MainBody (Capsule - Green)
│   ├── Head (Sphere - Light Green)
│   └── Weapon (Cube - Black)
├── FirePoint (Empty GameObject)
├── ReviveEffect (Particle System)
└── MuzzleFlash (Particle System)
```

---

## ⚙️ **Advanced Prefab Customization**

### **Visual Enhancements:**
- Replace primitive shapes with 3D models
- Add animations for walking/shooting
- Create custom materials with textures
- Add LED indicators for AI state

### **Functional Enhancements:**
- Add health system for AI teammate
- Implement different weapon types
- Create upgrade system for AI abilities
- Add voice lines for AI responses

### **Performance Optimizations:**
- Use LOD groups for distant rendering
- Optimize particle effects
- Implement object pooling for effects
- Add culling for off-screen AI

---

## 🎨 **Material Setup (Optional)**

### **Create Green Bot Material:**
1. **Right-click** in `Assets/Materials/`
2. **Create → Material**
3. **Name:** "BotMaterial"
4. **Set Albedo color** to bright green
5. **Assign to MainBody** renderer

### **Create Weapon Material:**
1. **Create another material** named "WeaponMaterial"
2. **Set color** to dark gray/black
3. **Add metallic properties** for realistic look
4. **Assign to Weapon** renderer

---

## 🔍 **Troubleshooting Prefab Issues**

### **AI Not Moving:**
- ✅ Check NavMeshAgent is enabled
- ✅ Verify NavMesh is baked in scene
- ✅ Ensure destination is on NavMesh surface

### **AI Not Following Player:**
- ✅ Check Player reference is assigned
- ✅ Verify followDistance settings
- ✅ Ensure no obstacles blocking path

### **Combat Not Working:**
- ✅ Check FirePoint is assigned
- ✅ Verify enemy layer mask (Layer 8)
- ✅ Ensure attackRange is appropriate

### **Revive Not Working:**
- ✅ Check Player has ReviveSystem component
- ✅ Verify reviveDistance and reviveTime
- ✅ Ensure AI can reach downed player

---

## 🎯 **Prefab Variants**

### **Create Specialized Variants:**

**Heavy Gunner Bot:**
- Increase damage and health
- Slower movement speed
- Larger weapon model
- Different color scheme

**Medic Bot:**
- Faster revive time
- Healing abilities
- Medical equipment visuals
- White/red color scheme

**Scout Bot:**
- Faster movement
- Longer detection range
- Smaller, agile appearance
- Blue/gray color scheme

---

## 📦 **Prefab Distribution**

### **Sharing Your Prefab:**
1. **Export as Unity Package:**
   - Select prefab and dependencies
   - Assets → Export Package
   - Include scripts and materials

2. **Version Control:**
   - Commit prefab files to repository
   - Include .meta files for proper references
   - Document any external dependencies

---

## 🎉 **Congratulations!**

Your **TeammateBot prefab** is now complete and ready for use! You can:
- ✅ Drag into any scene for instant AI teammate
- ✅ Customize appearance and behavior
- ✅ Create multiple variants for different roles
- ✅ Share with other developers

**Next Steps:**
- Test in different environments
- Create additional prefab variants
- Integrate with game progression systems
- Add multiplayer support

**Happy Bot Building! 🤖🚀**
