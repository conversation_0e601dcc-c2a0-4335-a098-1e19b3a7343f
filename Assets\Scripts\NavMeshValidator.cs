using UnityEngine;
using UnityEngine.AI;

/// <summary>
/// NavMesh validation and auto-fix system for Unity 6 AI Teammate Bot
/// Ensures all AI agents are properly placed on NavMesh
/// </summary>
public class NavMeshValidator : MonoBehaviour
{
    [Header("Validation Settings")]
    public bool autoValidateOnStart = true;
    public bool autoFixPositions = true;
    public float searchRadius = 10f;
    
    [Header("Debug")]
    public bool showDebugInfo = true;
    public Color validAgentColor = Color.green;
    public Color invalidAgentColor = Color.red;
    
    void Start()
    {
        if (autoValidateOnStart)
        {
            StartCoroutine(ValidateAfterFrame());
        }
    }
    
    System.Collections.IEnumerator ValidateAfterFrame()
    {
        // Wait for NavMesh to be fully loaded
        yield return new WaitForSeconds(0.5f);
        
        ValidateAllAgents();
    }
    
    [ContextMenu("Validate All NavMesh Agents")]
    public void ValidateAllAgents()
    {
        Debug.Log("🔍 Starting NavMesh validation...");
        
        // Check if NavMesh exists
        if (!HasNavMesh())
        {
            Debug.LogError("❌ No NavMesh found in scene! Please bake NavMesh first.");
            Debug.LogError("Go to Window > AI > Navigation, select ground objects, mark as 'Navigation Static', and click 'Bake'");
            return;
        }
        
        // Validate all NavMeshAgents
        NavMeshAgent[] agents = FindObjectsOfType<NavMeshAgent>();
        int validAgents = 0;
        int fixedAgents = 0;
        
        foreach (NavMeshAgent agent in agents)
        {
            if (ValidateAgent(agent))
            {
                validAgents++;
            }
            else if (autoFixPositions && TryFixAgent(agent))
            {
                fixedAgents++;
                validAgents++;
            }
        }
        
        Debug.Log($"✅ NavMesh validation complete: {validAgents}/{agents.Length} agents valid, {fixedAgents} fixed");
        
        // Validate specific AI components
        ValidateTeammateAI();
        ValidateEnemyAI();
    }
    
    bool HasNavMesh()
    {
        // Check if there's any NavMesh data in the scene
        NavMeshTriangulation triangulation = NavMesh.CalculateTriangulation();
        return triangulation.vertices.Length > 0;
    }
    
    bool ValidateAgent(NavMeshAgent agent)
    {
        if (agent == null) return false;
        
        bool isValid = agent.enabled && agent.isOnNavMesh && agent.isActiveAndEnabled;
        
        if (showDebugInfo)
        {
            string status = isValid ? "✅ VALID" : "❌ INVALID";
            Debug.Log($"{status} NavMeshAgent: {agent.gameObject.name} - OnNavMesh: {agent.isOnNavMesh}, Enabled: {agent.enabled}");
        }
        
        return isValid;
    }
    
    bool TryFixAgent(NavMeshAgent agent)
    {
        if (agent == null) return false;
        
        Debug.Log($"🔧 Attempting to fix NavMeshAgent: {agent.gameObject.name}");
        
        // Try to find nearest NavMesh position
        NavMeshHit hit;
        if (NavMesh.SamplePosition(agent.transform.position, out hit, searchRadius, NavMesh.AllAreas))
        {
            agent.transform.position = hit.position;
            
            // Wait a frame for the agent to update
            if (agent.isOnNavMesh)
            {
                Debug.Log($"✅ Fixed NavMeshAgent: {agent.gameObject.name} moved to {hit.position}");
                return true;
            }
        }
        
        // Try enabling the agent if it's disabled
        if (!agent.enabled)
        {
            agent.enabled = true;
            if (agent.isOnNavMesh)
            {
                Debug.Log($"✅ Fixed NavMeshAgent: {agent.gameObject.name} by enabling component");
                return true;
            }
        }
        
        Debug.LogWarning($"⚠️ Could not fix NavMeshAgent: {agent.gameObject.name}");
        return false;
    }
    
    void ValidateTeammateAI()
    {
        TeammateAI[] teammates = FindObjectsOfType<TeammateAI>();
        
        foreach (TeammateAI teammate in teammates)
        {
            NavMeshAgent agent = teammate.GetComponent<NavMeshAgent>();
            if (agent != null && !agent.isOnNavMesh)
            {
                Debug.LogWarning($"⚠️ TeammateAI '{teammate.gameObject.name}' has invalid NavMeshAgent");
                
                if (autoFixPositions)
                {
                    TryFixAgent(agent);
                }
            }
        }
        
        Debug.Log($"🤖 Validated {teammates.Length} TeammateAI instances");
    }
    
    void ValidateEnemyAI()
    {
        EnemyAI[] enemies = FindObjectsOfType<EnemyAI>();
        
        foreach (EnemyAI enemy in enemies)
        {
            NavMeshAgent agent = enemy.GetComponent<NavMeshAgent>();
            if (agent != null && !agent.isOnNavMesh)
            {
                Debug.LogWarning($"⚠️ EnemyAI '{enemy.gameObject.name}' has invalid NavMeshAgent");
                
                if (autoFixPositions)
                {
                    TryFixAgent(agent);
                }
            }
        }
        
        Debug.Log($"👾 Validated {enemies.Length} EnemyAI instances");
    }
    
    void Update()
    {
        // Manual validation hotkey
        if (Input.GetKeyDown(KeyCode.F12))
        {
            ValidateAllAgents();
        }
    }
    
    void OnDrawGizmos()
    {
        if (!showDebugInfo) return;
        
        // Draw NavMesh agents status
        NavMeshAgent[] agents = FindObjectsOfType<NavMeshAgent>();
        
        foreach (NavMeshAgent agent in agents)
        {
            if (agent == null) continue;
            
            bool isValid = agent.enabled && agent.isOnNavMesh && agent.isActiveAndEnabled;
            Gizmos.color = isValid ? validAgentColor : invalidAgentColor;
            
            // Draw agent position
            Gizmos.DrawWireSphere(agent.transform.position, 0.5f);
            
            // Draw search radius for invalid agents
            if (!isValid)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireSphere(agent.transform.position, searchRadius);
            }
        }
    }
    
    // Public utility methods
    public static bool IsAgentValid(NavMeshAgent agent)
    {
        return agent != null && agent.enabled && agent.isOnNavMesh && agent.isActiveAndEnabled;
    }
    
    public static bool TryPlaceOnNavMesh(Transform transform, float searchRadius = 10f)
    {
        NavMeshHit hit;
        if (NavMesh.SamplePosition(transform.position, out hit, searchRadius, NavMesh.AllAreas))
        {
            transform.position = hit.position;
            return true;
        }
        return false;
    }
    
    public static void SafeSetDestination(NavMeshAgent agent, Vector3 destination)
    {
        if (IsAgentValid(agent))
        {
            agent.SetDestination(destination);
        }
    }
    
    public static void SafeResetPath(NavMeshAgent agent)
    {
        if (IsAgentValid(agent))
        {
            agent.ResetPath();
        }
    }
    
    void OnGUI()
    {
        if (!showDebugInfo) return;
        
        GUI.Label(new Rect(10, Screen.height - 100, 300, 20), "NavMesh Validator Active");
        GUI.Label(new Rect(10, Screen.height - 80, 300, 20), "Press F12 to validate all agents");
        
        NavMeshAgent[] agents = FindObjectsOfType<NavMeshAgent>();
        int validCount = 0;
        
        foreach (NavMeshAgent agent in agents)
        {
            if (IsAgentValid(agent)) validCount++;
        }
        
        GUI.Label(new Rect(10, Screen.height - 60, 300, 20), $"NavMesh Agents: {validCount}/{agents.Length} valid");
        
        if (!HasNavMesh())
        {
            GUI.color = Color.red;
            GUI.Label(new Rect(10, Screen.height - 40, 400, 20), "⚠️ NO NAVMESH FOUND - Please bake NavMesh!");
            GUI.color = Color.white;
        }
    }
}
