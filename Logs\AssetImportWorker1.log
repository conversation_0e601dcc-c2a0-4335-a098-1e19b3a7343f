Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.21f1 (bf09ca542b87) revision 12519882'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 16235 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.21f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
C:/squadmateai
-logFile
Logs/AssetImportWorker1.log
-srvPort
61805
Successfully changed project path to: C:/squadmateai
C:/squadmateai
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [13744] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3781848587 [EditorId] 3781848587 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-9I4BNV2) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [13744] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3781848587 [EditorId] 3781848587 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-9I4BNV2) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[Physics::Module] Initialized MultithreadedJobDispatcher with 7 workers.
Refreshing native plugins compatible for Editor in 280.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.21f1 (bf09ca542b87)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.21f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/squadmateai/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1050 Ti (ID=0x1c8c)
    Vendor:   NVIDIA
    VRAM:     4004 MB
    Driver:   32.0.15.7283
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.21f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.21f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.21f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56872
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.21f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.21f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.21f1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Registered in 0.025023 seconds.
- Loaded All Assemblies, in  0.835 seconds
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 530 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.195 seconds
Domain Reload Profiling: 2027ms
	BeginReloadAssembly (237ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (112ms)
	LoadAllAssembliesAndSetupDomain (401ms)
		LoadAssemblies (235ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (395ms)
			TypeCache.Refresh (393ms)
				TypeCache.ScanAssembly (362ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1196ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1083ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (779ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (5ms)
			ProcessInitializeOnLoadAttributes (207ms)
			ProcessInitializeOnLoadMethodAttributes (87ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image C:\squadmateai\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image C:\squadmateai\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
- Loaded All Assemblies, in  3.218 seconds
Refreshing native plugins compatible for Editor in 11.67 ms, found 3 plugins.
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.306 seconds
Domain Reload Profiling: 4518ms
	BeginReloadAssembly (326ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (82ms)
	LoadAllAssembliesAndSetupDomain (2713ms)
		LoadAssemblies (2391ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (528ms)
			TypeCache.Refresh (411ms)
				TypeCache.ScanAssembly (366ms)
			ScanForSourceGeneratedMonoScriptInfo (89ms)
			ResolveRequiredComponents (26ms)
	FinalizeReload (1307ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (956ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (74ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (172ms)
			ProcessInitializeOnLoadAttributes (545ms)
			ProcessInitializeOnLoadMethodAttributes (145ms)
			AfterProcessingInitializeOnLoad (15ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 17.23 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5041 Unused Serialized files (Serialized files now loaded: 0)
Unloading 70 unused Assets / (0.8 MB). Loaded Objects now: 5497.
Memory consumption went from 195.1 MB to 194.4 MB.
Total: 15.652200 ms (FindLiveObjects: 1.312300 ms CreateObjectMapping: 4.178900 ms MarkObjects: 9.056400 ms  DeleteObjects: 1.103000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 85180.918215 seconds.
  path: Assets/UniversalRenderPipelineGlobalSettings.asset
  artifactKey: Guid(5a897f096d1cbf440a2f110208c5e14d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniversalRenderPipelineGlobalSettings.asset using Guid(5a897f096d1cbf440a2f110208c5e14d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '55aae9853231cccf0dc6b98bbae3f728') in 0.037645 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
TcpMessagingSession - receive error
AssetImportWorkerClient::OnTransportError - code=10054 error=An existing connection was forcibly closed by the remote host.
