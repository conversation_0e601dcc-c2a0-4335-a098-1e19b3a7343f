using UnityEngine;

/// <summary>
/// Simple test script to validate all components compile correctly
/// </summary>
public class CompilationTest : MonoBehaviour
{
    [Head<PERSON>("Test References")]
    public PlayerController player;
    public TeammateAI teammate;
    public InviteBotUI inviteUI;
    public VoiceFeedbackSystem voiceSystem;
    public SubtitleUI subtitleUI;
    public EnemyHealth enemyHealth;
    
    void Start()
    {
        Debug.Log("=== Unity 6 AI Teammate Bot - Compilation Test ===");
        
        // Test PlayerController
        if (player != null)
        {
            Debug.Log("✅ PlayerController found and accessible");
            Debug.Log($"Player Health: {player.GetHealthPercentage() * 100:F0}%");
        }
        
        // Test TeammateAI
        if (teammate != null)
        {
            Debug.Log("✅ TeammateAI found and accessible");
            Debug.Log($"AI State: {teammate.currentState}");
        }
        
        // Test InviteBotUI
        if (inviteUI != null)
        {
            Debug.Log("✅ InviteBotUI found and accessible");
        }
        
        // Test VoiceFeedbackSystem
        if (voiceSystem != null)
        {
            Debug.Log("✅ VoiceFeedbackSystem found and accessible");
            voiceSystem.PlayJoinVoice("Compilation test successful!");
        }
        
        // Test SubtitleUI
        if (subtitleUI != null)
        {
            Debug.Log("✅ SubtitleUI found and accessible");
            subtitleUI.ShowSubtitle("All systems operational!", 2f);
        }
        
        // Test EnemyHealth
        if (enemyHealth != null)
        {
            Debug.Log("✅ EnemyHealth found and accessible");
            Debug.Log($"Enemy Health: {enemyHealth.GetHealthPercentage() * 100:F0}%");
        }
        
        Debug.Log("=== Compilation Test Complete ===");
    }
    
    void Update()
    {
        // Test hotkeys
        if (Input.GetKeyDown(KeyCode.F10))
        {
            RunRuntimeTest();
        }
    }
    
    void RunRuntimeTest()
    {
        Debug.Log("=== Runtime Test Started ===");
        
        // Test player damage
        if (player != null)
        {
            player.TakeDamage(10f);
            Debug.Log("Player took 10 damage");
        }
        
        // Test AI voice
        if (voiceSystem != null)
        {
            voiceSystem.PlayCombatVoice("Runtime test in progress!");
        }
        
        // Test subtitle
        if (subtitleUI != null)
        {
            subtitleUI.ShowSubtitle("Runtime test successful!", 3f);
        }
        
        Debug.Log("=== Runtime Test Complete ===");
    }
    
    void OnGUI()
    {
        GUI.Label(new Rect(10, 10, 300, 20), "Unity 6 AI Teammate Bot - Ready!");
        GUI.Label(new Rect(10, 30, 300, 20), "Press F10 for runtime test");
        
        if (player != null)
        {
            GUI.Label(new Rect(10, 50, 300, 20), $"Player Health: {player.currentHealth:F0}/{player.maxHealth:F0}");
        }
        
        if (teammate != null)
        {
            GUI.Label(new Rect(10, 70, 300, 20), $"AI State: {teammate.currentState}");
        }
    }
}
